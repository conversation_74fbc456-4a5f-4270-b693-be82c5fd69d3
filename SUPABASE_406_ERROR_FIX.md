# Supabase 406 Error Fix

## Problem
The application was experiencing 406 "Not Acceptable" errors when making requests to the `content_calendars` table:

```
ezibvfaneuiyzcghyuuv.supabase.co/rest/v1/content_calendars?select=*&brand_id=eq.a770c7da-f648-45f8-8384-64c6bfeb30bf&user_id=eq.6b9dc2fb-3897-40a6-b43a-4efdf7e41450&month=eq.8&year=eq.2025&is_current=eq.true:1  Failed to load resource: the server responded with a status of 406 ()
```

## Root Cause
The issue was caused by **conflicting Row Level Security (RLS) policies** on the `content_calendars` table:

1. **File 1** (`20250108000001_rls_policies.sql`) created policies with names like:
   - `"Users can view own content calendars"`
   - `"Users can insert own content calendars"`

2. **File 2** (`002_add_content_versioning.sql`) tried to drop and recreate policies with different names:
   - `"Users can view their own content calendars"`
   - `"Users can insert their own content calendars"`

The `DROP POLICY IF EXISTS` statements failed because the policy names didn't match exactly, resulting in multiple conflicting policies on the same table.

## Solution Applied

### 1. Fixed RLS Policy Conflicts
Created `supabase/migrations/003_fix_rls_policy_conflicts.sql` to:
- Drop all existing conflicting policies
- Create clean, consistent policies with unique names
- Ensure proper RLS configuration

### 2. Improved Authentication Handling
Enhanced the dashboard authentication flow to:
- Check both session and user status
- Add better error logging
- Clear state properly on sign out
- Handle authentication errors more gracefully

### 3. Enhanced Error Handling
Updated `contentCalendarService.ts` to:
- Check authentication before making database requests
- Provide specific error messages for 406 errors
- Add detailed logging for debugging

## How to Apply the Fix

### Option 1: Manual Application (Recommended)
1. Go to your Supabase dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of `supabase/migrations/003_fix_rls_policy_conflicts.sql`
4. Execute the migration

### Option 2: Using Supabase CLI (if available)
```bash
supabase db push
```

## Migration SQL
```sql
-- Fix RLS policy conflicts for content_calendars table
-- Drop all existing policies on content_calendars table
DROP POLICY IF EXISTS "Users can view own content calendars" ON public.content_calendars;
DROP POLICY IF EXISTS "Users can view their own content calendars" ON public.content_calendars;
DROP POLICY IF EXISTS "Users can insert own content calendars" ON public.content_calendars;
DROP POLICY IF EXISTS "Users can insert their own content calendars" ON public.content_calendars;
DROP POLICY IF EXISTS "Users can update own content calendars" ON public.content_calendars;
DROP POLICY IF EXISTS "Users can update their own content calendars" ON public.content_calendars;
DROP POLICY IF EXISTS "Users can delete own content calendars" ON public.content_calendars;
DROP POLICY IF EXISTS "Users can delete their own content calendars" ON public.content_calendars;

-- Create clean, consistent policies for content_calendars
CREATE POLICY "content_calendars_select_policy" ON public.content_calendars
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "content_calendars_insert_policy" ON public.content_calendars
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "content_calendars_update_policy" ON public.content_calendars
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "content_calendars_delete_policy" ON public.content_calendars
    FOR DELETE USING (auth.uid() = user_id);

-- Ensure RLS is enabled
ALTER TABLE public.content_calendars ENABLE ROW LEVEL SECURITY;
```

## Verification
After applying the fix:
1. Clear browser cache and cookies
2. Sign out and sign back in
3. Try generating or loading calendar content
4. Check browser console for any remaining 406 errors

## Prevention
To prevent similar issues in the future:
1. Use consistent policy naming conventions
2. Always test policy changes in development first
3. Use unique, descriptive policy names
4. Document all RLS policy changes

## Additional Notes
- The favicon.ico 404 error has also been resolved by adding proper favicon files
- Enhanced authentication logging will help debug future auth issues
- The application now handles authentication state changes more robustly
