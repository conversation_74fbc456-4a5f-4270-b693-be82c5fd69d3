// Shared Content Types
// Centralized type definitions to avoid conflicts and ensure consistency

export interface ContentItem {
  id: string
  date: string
  platform: string
  type: string
  caption: string
  hashtags: string[]
  visualPrompt: string
  status: 'draft' | 'scheduled' | 'published'
  engagement: {
    likes: number
    comments: number
    shares: number
  }
  bestTime: string
}

export interface Brand {
  id: string
  name: string
  industry: string
  tone: string
  target_audience: string
  platforms: string[]
  description: string
  website: string
}

export interface BrandData {
  name: string
  industry: string
  tone: string
  targetAudience: string
  platforms: string[]
  description: string
  website: string
  bestTime: string
}

export interface User {
  id: string
  email: string
  // Add other user properties as needed
}

export interface Stats {
  totalPosts: number
  scheduledPosts: number
  publishedPosts: number
  totalEngagement: number
  avgEngagement: number
  topPlatform: string
  bestTime: string
}
