'use client'

import { BarChart3, Globe, Target, Clock, BookOpen, CheckSquare } from 'lucide-react'
import { StatsCardSkeleton, PlatformBreakdownSkeleton, BrandSettingsSkeleton } from '@/components/SkeletonLoader'

interface BrandData {
  name: string
  industry: string
  tone: string
  targetAudience: string
  platforms: string[]
}

interface Stats {
  totalPosts: number
  platformCounts: Record<string, number>
}

interface DashboardSidebarProps {
  brandData: BrandData | null
  stats: Stats
  isLoadingContent: boolean
  onBrandManagerOpen: () => void
  onPostingTimesOpen?: () => void
  onTemplateLibraryOpen?: () => void
  onBulkOperationsOpen?: () => void
}

export default function DashboardSidebar({
  brandData,
  stats,
  isLoadingContent,
  onBrandManagerOpen,
  onPostingTimesOpen,
  onTemplateLibraryOpen,
  onBulkOperationsOpen
}: DashboardSidebarProps) {
  if (!brandData || isLoadingContent) {
    return (
      <div className="lg:col-span-1">
        <div className="space-y-6">
          <StatsCardSkeleton />
          <PlatformBreakdownSkeleton />
          <BrandSettingsSkeleton />
        </div>
      </div>
    )
  }

  return (
    <div className="lg:col-span-1">
      <div className="space-y-6">
        {/* Overview Stats */}
        <div className="bg-white rounded-2xl p-6 shadow-soft border border-gray-100">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-10 h-10 bg-gradient-primary/10 rounded-xl flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-primary-purple" />
            </div>
            <h3 className="font-semibold text-neutral-dark">Overview</h3>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-xl">
              <div className="text-2xl font-bold text-primary-purple mb-1">
                {stats.totalPosts}
              </div>
              <div className="text-xs text-gray-600">Total Posts</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-xl">
              <div className="text-2xl font-bold text-primary-blue mb-1">
                {brandData.platforms.length}
              </div>
              <div className="text-xs text-gray-600">Platforms</div>
            </div>
          </div>
        </div>

        {/* Platform Breakdown */}
        <div className="bg-white rounded-2xl p-6 shadow-soft border border-gray-100">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-10 h-10 bg-gradient-primary/10 rounded-xl flex items-center justify-center">
              <Globe className="w-5 h-5 text-primary-blue" />
            </div>
            <h3 className="font-semibold text-neutral-dark">Platforms</h3>
          </div>
          <div className="space-y-3">
            {brandData.platforms.map((platform) => (
              <div key={platform} className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center text-white text-xs font-bold">
                    {platform.charAt(0).toUpperCase()}
                  </div>
                  <span className="text-sm font-medium text-gray-900 capitalize">{platform}</span>
                </div>
                <span className="font-bold text-primary-purple">
                  {stats.platformCounts[platform] || 0}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Brand Profile */}
        <div className="bg-white rounded-2xl p-6 shadow-soft border border-gray-100">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-10 h-10 bg-gradient-primary/10 rounded-xl flex items-center justify-center">
              <Target className="w-5 h-5 text-primary-purple" />
            </div>
            <h3 className="font-semibold text-neutral-dark">Brand Profile</h3>
          </div>
          <div className="space-y-4">
            <div className="p-3 bg-gray-50 rounded-xl">
              <div className="text-xs text-gray-500 mb-1">Tone</div>
              <div className="font-semibold text-neutral-dark capitalize">
                {brandData.tone}
              </div>
            </div>
            <div className="p-3 bg-gray-50 rounded-xl">
              <div className="text-xs text-gray-500 mb-1">Target Audience</div>
              <div className="font-semibold text-neutral-dark">
                {brandData.targetAudience}
              </div>
            </div>
            <div className="p-3 bg-gray-50 rounded-xl">
              <div className="text-xs text-gray-500 mb-1">Industry</div>
              <div className="font-semibold text-neutral-dark capitalize">
                {brandData.industry}
              </div>
            </div>
          </div>
          <button
            onClick={onBrandManagerOpen}
            className="w-full mt-6 bg-gradient-primary/10 text-primary-purple hover:bg-gradient-primary hover:text-white transition-all duration-200 py-3 rounded-xl text-sm font-medium"
          >
            Edit Brand Settings
          </button>
        </div>

        {/* Content Templates */}
        <div className="bg-white rounded-2xl p-6 shadow-soft border border-gray-100">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-gradient-primary/10 rounded-xl flex items-center justify-center">
              <BookOpen className="w-5 h-5 text-primary-purple" />
            </div>
            <h3 className="font-semibold text-neutral-dark">Templates</h3>
          </div>
          <p className="text-sm text-gray-600 mb-4">
            Professional content templates for your industry with customizable variations
          </p>
          <button
            onClick={onTemplateLibraryOpen}
            className="w-full bg-gradient-primary/10 text-primary-purple hover:bg-gradient-primary hover:text-white transition-all duration-200 py-3 rounded-xl text-sm font-medium"
          >
            Browse Templates
          </button>
        </div>

        {/* Optimal Posting Times */}
        <div className="bg-white rounded-2xl p-6 shadow-soft border border-gray-100">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-gradient-primary/10 rounded-xl flex items-center justify-center">
              <Clock className="w-5 h-5 text-primary-purple" />
            </div>
            <h3 className="font-semibold text-neutral-dark">Optimal Times</h3>
          </div>
          <p className="text-sm text-gray-600 mb-4">
            AI-powered posting time recommendations for maximum engagement
          </p>
          <button
            onClick={onPostingTimesOpen}
            className="w-full bg-gradient-primary/10 text-primary-purple hover:bg-gradient-primary hover:text-white transition-all duration-200 py-3 rounded-xl text-sm font-medium"
          >
            View Recommendations
          </button>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-2xl p-6 shadow-soft border border-gray-100">
          <h3 className="font-semibold text-neutral-dark mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button className="w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-xl transition-colors text-sm">
              📊 View Analytics
            </button>
            <button className="w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-xl transition-colors text-sm">
              🎯 Content Ideas
            </button>
            <button className="w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-xl transition-colors text-sm">
              📅 Schedule Posts
            </button>
            <button
              onClick={onBulkOperationsOpen}
              className="w-full text-left p-3 bg-gradient-primary/10 text-primary-purple hover:bg-gradient-primary hover:text-white rounded-xl transition-all duration-200 text-sm font-medium flex items-center gap-2"
            >
              <CheckSquare className="w-4 h-4" />
              Bulk Operations
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
