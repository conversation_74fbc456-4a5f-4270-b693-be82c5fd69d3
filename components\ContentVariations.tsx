'use client'

import { useState, useEffect } from 'react'
import { X, RefreshCw, TrendingUp, Target, Zap, CheckCircle, AlertCircle } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { generateContentVariations, analyzeContentPotential } from '@/lib/ai-service'
import type { ContentItem } from '@/lib/services/contentCalendarService'

interface ContentVariationsProps {
  brandData: {
    brandName: string
    industry: string
    tone: string
    targetAudience: string
    platforms: string[]
  }
  date: Date
  platform: string
  onClose: () => void
  onSelectVariation: (content: ContentItem) => void
}

interface ContentAnalysis {
  engagementScore: number
  viralPotential: number
  brandAlignment: number
  recommendations: string[]
}

export default function ContentVariations({
  brandData,
  date,
  platform,
  onClose,
  onSelectVariation
}: ContentVariationsProps) {
  const [variations, setVariations] = useState<ContentItem[]>([])
  const [analyses, setAnalyses] = useState<Record<string, ContentAnalysis>>({})
  const [loading, setLoading] = useState(false)
  const [selectedVariation, setSelectedVariation] = useState<string | null>(null)

  useEffect(() => {
    generateVariations()
  }, [])

  const generateVariations = async () => {
    setLoading(true)
    try {
      const newVariations = await generateContentVariations(brandData, date, platform, 3)
      setVariations(newVariations)
      
      // Analyze each variation
      const newAnalyses: Record<string, ContentAnalysis> = {}
      newVariations.forEach(variation => {
        newAnalyses[variation.id] = analyzeContentPotential(variation)
      })
      setAnalyses(newAnalyses)
    } catch (error) {
      console.error('Error generating variations:', error)
      toast.error('Failed to generate content variations')
    } finally {
      setLoading(false)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100'
    if (score >= 60) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const getScoreIcon = (score: number) => {
    if (score >= 80) return <CheckCircle className="w-4 h-4" />
    if (score >= 60) return <AlertCircle className="w-4 h-4" />
    return <AlertCircle className="w-4 h-4" />
  }

  const handleSelectVariation = (variation: ContentItem) => {
    onSelectVariation(variation)
    toast.success('Content variation selected!')
    onClose()
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-neutral-dark flex items-center gap-2">
              <Zap className="w-5 h-5 text-primary-purple" />
              Content Variations
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              AI-generated variations for {platform} • {date.toLocaleDateString()}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={generateVariations}
              disabled={loading}
              className="btn-secondary flex items-center gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              Regenerate
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-150"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-purple mx-auto mb-4"></div>
              <p className="text-gray-600">Generating content variations...</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {variations.map((variation, index) => {
                const analysis = analyses[variation.id]
                const isSelected = selectedVariation === variation.id

                return (
                  <div
                    key={variation.id}
                    className={`border rounded-xl p-6 transition-all duration-200 cursor-pointer ${
                      isSelected
                        ? 'border-primary-purple bg-primary-purple/5 shadow-lg'
                        : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                    }`}
                    onClick={() => setSelectedVariation(variation.id)}
                  >
                    {/* Variation Header */}
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold text-neutral-dark">
                        Variation {index + 1}
                      </h3>
                      <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full capitalize">
                        {variation.type}
                      </span>
                    </div>

                    {/* Performance Scores */}
                    {analysis && (
                      <div className="grid grid-cols-3 gap-2 mb-4">
                        <div className="text-center">
                          <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(analysis.engagementScore)}`}>
                            <TrendingUp className="w-3 h-3" />
                            {analysis.engagementScore}
                          </div>
                          <p className="text-xs text-gray-600 mt-1">Engagement</p>
                        </div>
                        <div className="text-center">
                          <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(analysis.viralPotential)}`}>
                            <Zap className="w-3 h-3" />
                            {analysis.viralPotential}
                          </div>
                          <p className="text-xs text-gray-600 mt-1">Viral</p>
                        </div>
                        <div className="text-center">
                          <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(analysis.brandAlignment)}`}>
                            <Target className="w-3 h-3" />
                            {analysis.brandAlignment}
                          </div>
                          <p className="text-xs text-gray-600 mt-1">Brand</p>
                        </div>
                      </div>
                    )}

                    {/* Caption Preview */}
                    <div className="mb-4">
                      <p className="text-sm text-gray-800 line-clamp-4">
                        {variation.caption}
                      </p>
                    </div>

                    {/* Hashtags */}
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-1">
                        {variation.hashtags.slice(0, 3).map((tag, tagIndex) => (
                          <span key={tagIndex} className="text-xs text-primary-blue">
                            {tag}
                          </span>
                        ))}
                        {variation.hashtags.length > 3 && (
                          <span className="text-xs text-gray-500">
                            +{variation.hashtags.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Recommendations */}
                    {analysis && analysis.recommendations.length > 0 && (
                      <div className="mb-4">
                        <h4 className="text-xs font-medium text-gray-700 mb-2">Recommendations:</h4>
                        <ul className="text-xs text-gray-600 space-y-1">
                          {analysis.recommendations.slice(0, 2).map((rec, recIndex) => (
                            <li key={recIndex} className="flex items-start gap-1">
                              <span className="text-primary-purple">•</span>
                              {rec}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Select Button */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleSelectVariation(variation)
                      }}
                      className={`w-full py-2 px-4 rounded-lg text-sm font-medium transition-colors duration-150 ${
                        isSelected
                          ? 'bg-primary-purple text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {isSelected ? 'Selected' : 'Use This Variation'}
                    </button>
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
