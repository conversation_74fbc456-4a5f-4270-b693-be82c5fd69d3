'use client'

import { useState, useEffect } from 'react'
import { Clock, TrendingUp, Target, Info, Calendar, Zap } from 'lucide-react'
import { PostingTimeService } from '@/lib/services/postingTimeService'

interface PostingRecommendation {
  time: string
  dayOfWeek: number
  score: number
  reasoning: string[]
  timezone: string
}

interface PostingTimeRecommendationsProps {
  brandData: {
    industry: string
    targetAudience: string
    platforms: string[]
  }
  onClose: () => void
}

export default function PostingTimeRecommendations({
  brandData,
  onClose
}: PostingTimeRecommendationsProps) {
  const [recommendations, setRecommendations] = useState<Record<string, PostingRecommendation[]>>({})
  const [selectedPlatform, setSelectedPlatform] = useState<string>(brandData.platforms[0] || 'instagram')
  const [loading, setLoading] = useState(true)
  const [quickRecs, setQuickRecs] = useState<PostingRecommendation[]>([])

  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

  // Map target audience to age group
  const getAudienceProfile = (targetAudience: string) => {
    const audience = targetAudience.toLowerCase()
    if (audience.includes('gen z') || audience.includes('teenager') || audience.includes('young')) {
      return {
        primaryTimezone: 'America/New_York',
        ageGroup: 'gen-z' as const,
        workSchedule: 'flexible' as const,
        deviceUsage: 'mobile-first' as const
      }
    } else if (audience.includes('millennial') || audience.includes('young adult')) {
      return {
        primaryTimezone: 'America/New_York',
        ageGroup: 'millennial' as const,
        workSchedule: 'traditional' as const,
        deviceUsage: 'mixed' as const
      }
    } else if (audience.includes('gen x') || audience.includes('middle-aged')) {
      return {
        primaryTimezone: 'America/New_York',
        ageGroup: 'gen-x' as const,
        workSchedule: 'traditional' as const,
        deviceUsage: 'desktop-first' as const
      }
    } else {
      return {
        primaryTimezone: 'America/New_York',
        ageGroup: 'millennial' as const,
        workSchedule: 'mixed' as const,
        deviceUsage: 'mixed' as const
      }
    }
  }

  useEffect(() => {
    const generateRecommendations = async () => {
      setLoading(true)
      try {
        const audienceProfile = getAudienceProfile(brandData.targetAudience)
        const newRecommendations: Record<string, PostingRecommendation[]> = {}

        // Generate recommendations for each platform
        for (const platform of brandData.platforms) {
          try {
            const platformRecs = PostingTimeService.getOptimalTimes(
              platform,
              'mixed', // Default content type
              audienceProfile,
              brandData.industry
            )
            newRecommendations[platform] = platformRecs
          } catch (error) {
            console.error(`Error generating recommendations for ${platform}:`, error)
            newRecommendations[platform] = []
          }
        }

        setRecommendations(newRecommendations)

        // Get quick recommendations for the selected platform
        const quickRecommendations = PostingTimeService.getQuickRecommendations(selectedPlatform)
        setQuickRecs(quickRecommendations)
      } catch (error) {
        console.error('Error generating posting recommendations:', error)
      } finally {
        setLoading(false)
      }
    }

    generateRecommendations()
  }, [brandData, selectedPlatform])

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-100'
    if (score >= 80) return 'text-blue-600 bg-blue-100'
    if (score >= 70) return 'text-yellow-600 bg-yellow-100'
    return 'text-gray-600 bg-gray-100'
  }

  const getScoreIcon = (score: number) => {
    if (score >= 90) return <Zap className="w-4 h-4" />
    if (score >= 80) return <TrendingUp className="w-4 h-4" />
    if (score >= 70) return <Target className="w-4 h-4" />
    return <Clock className="w-4 h-4" />
  }

  const formatTime = (time: string) => {
    const [hour, minute] = time.split(':').map(Number)
    const ampm = hour >= 12 ? 'PM' : 'AM'
    const displayHour = hour % 12 || 12
    return `${displayHour}:${minute.toString().padStart(2, '0')} ${ampm}`
  }

  const currentPlatformRecs = recommendations[selectedPlatform] || []

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-5xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-neutral-dark flex items-center gap-2">
              <Clock className="w-5 h-5 text-primary-purple" />
              Optimal Posting Times
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              AI-powered recommendations based on your audience and industry
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-150"
          >
            ✕
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-purple mx-auto mb-4"></div>
              <p className="text-gray-600">Analyzing optimal posting times...</p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Quick Recommendations */}
              {quickRecs.length > 0 && (
                <div className="bg-gradient-primary/5 rounded-xl p-6">
                  <h3 className="font-semibold text-neutral-dark mb-4 flex items-center gap-2">
                    <Zap className="w-5 h-5 text-primary-purple" />
                    Post Now Recommendations
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {quickRecs.map((rec, index) => (
                      <div key={index} className="bg-white rounded-lg p-4 border border-gray-200">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-lg">{formatTime(rec.time)}</span>
                          <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(rec.score)}`}>
                            {getScoreIcon(rec.score)}
                            {rec.score}
                          </div>
                        </div>
                        <p className="text-sm text-gray-600">{rec.reasoning[0]}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Platform Selector */}
              <div className="flex items-center gap-4">
                <h3 className="font-semibold text-neutral-dark">Platform:</h3>
                <div className="flex gap-2">
                  {brandData.platforms.map((platform) => (
                    <button
                      key={platform}
                      onClick={() => setSelectedPlatform(platform)}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors capitalize ${
                        selectedPlatform === platform
                          ? 'bg-primary-purple text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {platform}
                    </button>
                  ))}
                </div>
              </div>

              {/* Weekly Recommendations */}
              <div className="bg-white rounded-xl border border-gray-200">
                <div className="p-4 border-b border-gray-100">
                  <h3 className="font-semibold text-neutral-dark flex items-center gap-2">
                    <Calendar className="w-5 h-5 text-primary-blue" />
                    Weekly Schedule for {selectedPlatform.charAt(0).toUpperCase() + selectedPlatform.slice(1)}
                  </h3>
                </div>
                <div className="p-4">
                  {currentPlatformRecs.length > 0 ? (
                    <div className="space-y-4">
                      {currentPlatformRecs.slice(0, 7).map((rec, index) => (
                        <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-4">
                            <div className="text-center">
                              <div className="font-medium text-sm text-gray-900">
                                {dayNames[rec.dayOfWeek]}
                              </div>
                              <div className="text-lg font-bold text-primary-purple">
                                {formatTime(rec.time)}
                              </div>
                            </div>
                            <div className="flex-1">
                              <div className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium mb-2 ${getScoreColor(rec.score)}`}>
                                {getScoreIcon(rec.score)}
                                Score: {rec.score}/100
                              </div>
                              <div className="space-y-1">
                                {rec.reasoning.map((reason, reasonIndex) => (
                                  <div key={reasonIndex} className="text-sm text-gray-600 flex items-start gap-2">
                                    <Info className="w-3 h-3 mt-0.5 text-gray-400 flex-shrink-0" />
                                    {reason}
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Clock className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-600">No recommendations available for {selectedPlatform}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Tips Section */}
              <div className="bg-blue-50 rounded-xl p-6">
                <h3 className="font-semibold text-neutral-dark mb-4 flex items-center gap-2">
                  <Target className="w-5 h-5 text-blue-600" />
                  Optimization Tips
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Industry: {brandData.industry}</h4>
                    <ul className="space-y-1 text-gray-600">
                      <li>• Consider your audience's work schedule</li>
                      <li>• Test different time slots for 2-3 weeks</li>
                      <li>• Monitor engagement patterns</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Audience: {brandData.targetAudience}</h4>
                    <ul className="space-y-1 text-gray-600">
                      <li>• Adjust for your audience's timezone</li>
                      <li>• Consider seasonal behavior changes</li>
                      <li>• Track performance and refine timing</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
