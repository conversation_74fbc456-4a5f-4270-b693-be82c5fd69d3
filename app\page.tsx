'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  Calendar,
  Sparkles,
  Target,
  Users,
  ArrowRight,
  CheckCircle,
  Zap,
  TrendingUp,
  Clock,
  BarChart3,
  <PERSON><PERSON>,
  Globe,
  Star,
  Play
} from 'lucide-react'
import OnboardingModal from '@/components/OnboardingModal'
import Header from '@/components/Header'
import Footer from '@/components/Footer'
import { createClient } from '@/lib/supabase/client'

export default function HomePage() {
  const [showOnboarding, setShowOnboarding] = useState(false)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    // Check if user is already authenticated
    const checkAuth = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (user) {
          // User is authenticated, redirect to dashboard
          router.push('/dashboard')
          return
        }
      } catch (error) {
        console.error('Auth check error:', error)
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [router, supabase.auth])

  const features = [
    {
      icon: <Calendar className="w-8 h-8 text-primary-purple" />,
      title: "AI-Powered Calendar Generation",
      description: "Generate complete 30-day content calendars with industry-specific templates, captions, hashtags, and visual prompts tailored to your brand.",
      highlight: "30-day calendars"
    },
    {
      icon: <Sparkles className="w-8 h-8 text-primary-blue" />,
      title: "Multi-Platform Optimization",
      description: "Create platform-specific content for Instagram, TikTok, LinkedIn, and Twitter with optimized formats and engagement tactics.",
      highlight: "4 platforms"
    },
    {
      icon: <Zap className="w-8 h-8 text-primary-purple" />,
      title: "Content Variations & A/B Testing",
      description: "Generate multiple content variations with performance analysis to help you choose the best-performing posts.",
      highlight: "A/B testing"
    },
    {
      icon: <TrendingUp className="w-8 h-8 text-primary-blue" />,
      title: "Performance Analytics",
      description: "Get engagement scores, viral potential ratings, and brand alignment analysis for every piece of content.",
      highlight: "Smart analytics"
    },
    {
      icon: <Clock className="w-8 h-8 text-primary-purple" />,
      title: "Optimal Timing Intelligence",
      description: "AI-powered recommendations for the best posting times based on platform algorithms and audience behavior.",
      highlight: "Perfect timing"
    },
    {
      icon: <Palette className="w-8 h-8 text-primary-blue" />,
      title: "Brand Voice Consistency",
      description: "Maintain your unique brand voice and tone across all content with industry-specific templates and guidelines.",
      highlight: "Brand consistency"
    }
  ]

  const benefits = [
    {
      icon: <BarChart3 className="w-5 h-5 text-green-500" />,
      text: "Reduce content planning time by 70%"
    },
    {
      icon: <Calendar className="w-5 h-5 text-blue-500" />,
      text: "Generate 30 days of content in minutes"
    },
    {
      icon: <Target className="w-5 h-5 text-purple-500" />,
      text: "Optimize for each platform's algorithm"
    },
    {
      icon: <Globe className="w-5 h-5 text-indigo-500" />,
      text: "Support for 4 major social platforms"
    },
    {
      icon: <Sparkles className="w-5 h-5 text-pink-500" />,
      text: "AI-powered viral content suggestions"
    },
    {
      icon: <Users className="w-5 h-5 text-orange-500" />,
      text: "Industry-specific content templates"
    }
  ]

  const stats = [
    { number: "70%", label: "Time Saved", description: "Average reduction in content planning time" },
    { number: "4", label: "Platforms", description: "Instagram, TikTok, LinkedIn, Twitter" },
    { number: "30", label: "Days", description: "Complete monthly content calendars" },
    { number: "10K+", label: "Posts", description: "Generated by our AI system" }
  ]

  const testimonials = [
    {
      name: "Sarah Chen",
      role: "Social Media Manager",
      company: "TechStart Inc.",
      content: "ViralPath.ai transformed our content strategy. We went from spending 8 hours a week planning to just 2 hours, and our engagement increased by 150%!",
      rating: 5
    },
    {
      name: "Marcus Rodriguez",
      role: "Content Creator",
      company: "@FitnessWithMarcus",
      content: "The AI-generated content variations are incredible. I can A/B test different approaches and the analytics help me choose the best performing content.",
      rating: 5
    },
    {
      name: "Emily Watson",
      role: "Marketing Director",
      company: "GreenLeaf Wellness",
      content: "Finally, a tool that understands our brand voice! The industry-specific templates for health & wellness are spot-on and save us so much time.",
      rating: 5
    }
  ]

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-neutral-light via-white to-neutral-light flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-purple mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-light via-white to-neutral-light">
      <Header onGetStarted={() => setShowOnboarding(true)} />

      {/* Hero Section */}
      <section className="relative pt-20 pb-20 px-4 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-purple/5 via-transparent to-primary-blue/5"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary-purple/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-primary-blue/10 rounded-full blur-3xl"></div>

        <div className="relative max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-gradient-primary text-white px-6 py-3 rounded-full text-sm font-medium mb-8 shadow-lg">
              <Sparkles className="w-4 h-4" />
              AI-Powered Content Generation
              <span className="bg-white/20 px-2 py-1 rounded-full text-xs ml-2">NEW</span>
            </div>

            <h1 className="text-6xl md:text-8xl font-heading font-bold text-neutral-dark mb-8 leading-tight">
              Create <span className="bg-gradient-primary bg-clip-text text-transparent">Viral</span> Content
              <br />Calendars in <span className="relative">
                Minutes
                <div className="absolute -bottom-2 left-0 right-0 h-3 bg-gradient-primary/20 rounded-full"></div>
              </span>
            </h1>

            <p className="text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
              Generate 30-day multi-platform social media calendars with AI. Get industry-specific content,
              performance analytics, and viral-worthy posts that drive engagement across all your channels.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
              <button
                onClick={() => setShowOnboarding(true)}
                className="group bg-gradient-primary text-white font-semibold px-10 py-5 rounded-2xl text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl flex items-center gap-3"
              >
                Start Creating Free
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </button>
              <button className="group flex items-center gap-3 text-lg font-medium text-gray-700 hover:text-primary-purple transition-colors">
                <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center group-hover:shadow-xl transition-shadow">
                  <Play className="w-5 h-5 text-primary-purple ml-1" />
                </div>
                Watch Demo
              </button>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
            {stats.map((stat, index) => (
              <div key={index} className="text-center group">
                <div className="bg-white rounded-2xl p-6 shadow-soft hover:shadow-lg transition-all duration-300 group-hover:scale-105">
                  <div className="text-4xl md:text-5xl font-bold bg-gradient-primary bg-clip-text text-transparent mb-2">
                    {stat.number}
                  </div>
                  <div className="text-lg font-semibold text-neutral-dark mb-1">{stat.label}</div>
                  <div className="text-sm text-gray-600">{stat.description}</div>
                </div>
              </div>
            ))}
          </div>

          {/* Social Proof */}
          <div className="text-center">
            <p className="text-gray-600 mb-6">Trusted by 10,000+ creators and marketers worldwide</p>
            <div className="flex justify-center items-center gap-2 mb-4">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
              ))}
              <span className="ml-2 text-gray-700 font-medium">4.9/5 from 2,000+ reviews</span>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-20">
            <div className="inline-block bg-gradient-primary/10 text-primary-purple px-4 py-2 rounded-full text-sm font-medium mb-4">
              Powerful Features
            </div>
            <h2 className="text-5xl md:text-6xl font-heading font-bold text-neutral-dark mb-6">
              Everything You Need to
              <br />
              <span className="bg-gradient-primary bg-clip-text text-transparent">Go Viral</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Advanced AI tools designed for modern marketers, creators, and social media managers
              who want to dominate their industry.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="group relative">
                <div className="bg-white rounded-3xl p-8 shadow-soft hover:shadow-2xl transition-all duration-500 group-hover:scale-105 border border-gray-100 h-full">
                  {/* Icon with gradient background */}
                  <div className="relative mb-6">
                    <div className="w-16 h-16 bg-gradient-primary/10 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                      {feature.icon}
                    </div>
                    <div className="absolute -top-2 -right-2 bg-gradient-primary text-white text-xs px-2 py-1 rounded-full font-medium">
                      {feature.highlight}
                    </div>
                  </div>

                  <h3 className="text-xl font-bold text-neutral-dark mb-4 group-hover:text-primary-purple transition-colors">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>

                  {/* Hover effect gradient border */}
                  <div className="absolute inset-0 rounded-3xl bg-gradient-primary opacity-0 group-hover:opacity-10 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 px-4 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <div className="inline-block bg-green-100 text-green-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
              Customer Success Stories
            </div>
            <h2 className="text-4xl md:text-5xl font-heading font-bold text-neutral-dark mb-4">
              Loved by Creators Worldwide
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See how ViralPath.ai is transforming content strategies for thousands of users
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-soft hover:shadow-lg transition-all duration-300">
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-700 mb-6 leading-relaxed italic">
                  "{testimonial.content}"
                </p>
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center text-white font-bold">
                    {testimonial.name.charAt(0)}
                  </div>
                  <div>
                    <div className="font-semibold text-neutral-dark">{testimonial.name}</div>
                    <div className="text-sm text-gray-600">{testimonial.role}</div>
                    <div className="text-sm text-primary-purple font-medium">{testimonial.company}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-heading font-bold text-neutral-dark mb-4">
              Why Choose ViralPath.ai?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Join thousands of successful creators and marketers
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center gap-4 p-4 rounded-xl hover:bg-gray-50 transition-colors">
                <div className="flex-shrink-0 w-10 h-10 bg-gradient-primary/10 rounded-lg flex items-center justify-center">
                  {benefit.icon}
                </div>
                <span className="text-lg text-neutral-dark font-medium">{benefit.text}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-24 px-4 bg-gradient-primary overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-white/5 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[length:60px_60px]"></div>
        </div>

        <div className="relative max-w-5xl mx-auto text-center">
          <div className="inline-block bg-white/20 text-white px-4 py-2 rounded-full text-sm font-medium mb-6">
            🚀 Ready to Go Viral?
          </div>

          <h2 className="text-5xl md:text-6xl font-heading font-bold text-white mb-6 leading-tight">
            Transform Your Content Strategy
            <br />
            <span className="text-white/80">in Minutes, Not Hours</span>
          </h2>

          <p className="text-xl text-white/90 mb-12 max-w-3xl mx-auto leading-relaxed">
            Join 10,000+ creators and marketers who've revolutionized their social media presence with AI.
            Start creating viral content today – completely free.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
            <button
              onClick={() => setShowOnboarding(true)}
              className="group bg-white text-primary-purple font-bold px-10 py-5 rounded-2xl text-lg hover:scale-105 transition-all duration-300 hover:shadow-2xl flex items-center gap-3"
            >
              Start Creating Free
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </button>
            <div className="text-white/80 text-sm">
              ✓ No credit card required  ✓ 30-day free trial  ✓ Cancel anytime
            </div>
          </div>

          {/* Trust indicators */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-white/70">
            <div className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              <span>10,000+ active users</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="w-5 h-5 text-yellow-300" />
              <span>4.9/5 rating</span>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="w-5 h-5" />
              <span>1M+ posts generated</span>
            </div>
          </div>
        </div>
      </section>

      <Footer />

      {showOnboarding && (
        <OnboardingModal
          onClose={() => setShowOnboarding(false)}
          onBrandCreated={() => {
            setShowOnboarding(false)
            router.push('/dashboard')
          }}
        />
      )}
    </div>
  )
}