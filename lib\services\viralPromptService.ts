// Viral Prompt Engineering Framework for ViralPath.ai
// Ensures AI consistently produces viral-ready, platform-optimized content

export interface ViralPromptConfig {
  platform: string
  contentType: string
  viralityScore: number // 1-10 scale
  engagementGoal: 'awareness' | 'engagement' | 'conversion' | 'viral'
  trendAlignment: boolean
  emotionalTrigger: string[]
}

export interface PlatformOptimization {
  characterLimits: {
    caption: number
    hashtags: number
  }
  viralElements: string[]
  bestPractices: string[]
  contentFormats: string[]
  peakTimes: string[]
}

export class ViralPromptService {
  // Platform-specific optimization rules
  private static readonly PLATFORM_RULES: Record<string, PlatformOptimization> = {
    instagram: {
      characterLimits: { caption: 2200, hashtags: 30 },
      viralElements: [
        'Visual storytelling hooks',
        'Carousel engagement tactics',
        'Story-to-feed cross-promotion',
        'Reel trending audio integration',
        'User-generated content triggers'
      ],
      bestPractices: [
        'First 3 words must hook attention',
        'Include clear call-to-action',
        'Use 5-10 strategic hashtags',
        'Add line breaks for readability',
        'Include emoji for visual appeal',
        'Ask engaging questions',
        'Create shareable moments'
      ],
      contentFormats: ['carousel', 'reel', 'story', 'post', 'igtv'],
      peakTimes: ['11:00-13:00', '17:00-19:00', '20:00-21:00']
    },
    tiktok: {
      characterLimits: { caption: 150, hashtags: 100 },
      viralElements: [
        'Trending sound integration',
        'Challenge participation',
        'Quick hook (first 3 seconds)',
        'Duet/stitch opportunities',
        'Educational quick tips'
      ],
      bestPractices: [
        'Hook within first 3 seconds',
        'Use trending sounds/music',
        'Keep captions short and punchy',
        'Include trending hashtags',
        'Create loop-worthy content',
        'Add text overlays for accessibility',
        'End with strong CTA'
      ],
      contentFormats: ['short-video', 'trend', 'challenge', 'educational', 'behind-scenes'],
      peakTimes: ['06:00-10:00', '19:00-23:00']
    },
    linkedin: {
      characterLimits: { caption: 3000, hashtags: 30 },
      viralElements: [
        'Professional storytelling',
        'Industry insights sharing',
        'Career advice hooks',
        'Business case studies',
        'Thought leadership content'
      ],
      bestPractices: [
        'Start with compelling question or stat',
        'Share personal/professional insights',
        'Use professional tone with personality',
        'Include industry-relevant hashtags',
        'Add document/carousel for engagement',
        'End with discussion starter',
        'Tag relevant connections'
      ],
      contentFormats: ['article', 'post', 'poll', 'document', 'video'],
      peakTimes: ['08:00-10:00', '12:00-14:00', '17:00-18:00']
    },
    twitter: {
      characterLimits: { caption: 280, hashtags: 280 },
      viralElements: [
        'Thread storytelling',
        'Real-time commentary',
        'Viral tweet formats',
        'Quote tweet opportunities',
        'Trending topic integration'
      ],
      bestPractices: [
        'Lead with strong hook',
        'Use thread format for longer content',
        'Include relevant trending hashtags',
        'Add visual elements when possible',
        'Engage with trending topics',
        'Use conversational tone',
        'End with retweet-worthy statement'
      ],
      contentFormats: ['tweet', 'thread', 'poll', 'quote-tweet', 'reply'],
      peakTimes: ['09:00-10:00', '12:00-13:00', '17:00-18:00', '20:00-21:00']
    }
  }

  // Viral content psychology triggers
  private static readonly VIRAL_TRIGGERS = {
    emotional: [
      'surprise', 'joy', 'anger', 'fear', 'sadness', 'disgust',
      'anticipation', 'trust', 'curiosity', 'nostalgia'
    ],
    social: [
      'social proof', 'authority', 'scarcity', 'reciprocity',
      'commitment', 'liking', 'consensus', 'urgency'
    ],
    cognitive: [
      'pattern interrupt', 'cognitive dissonance', 'confirmation bias',
      'availability heuristic', 'anchoring', 'loss aversion'
    ]
  }

  // Content virality patterns
  private static readonly VIRAL_PATTERNS = {
    hooks: [
      'POV: You just discovered...',
      'This will change how you think about...',
      'Nobody talks about this, but...',
      'I wish someone told me this 5 years ago...',
      'The secret that [industry] doesn\'t want you to know...',
      'Here\'s what happened when I...',
      'Stop doing [common thing]. Do this instead...',
      'This [number]-second trick will...',
      'Why [popular belief] is actually wrong...',
      'The real reason why...'
    ],
    structures: [
      'Problem → Agitation → Solution',
      'Before → After → How',
      'Mistake → Lesson → Application',
      'Question → Story → Answer',
      'Trend → Analysis → Prediction',
      'Challenge → Process → Result'
    ],
    endings: [
      'What would you add to this list?',
      'Which one surprised you the most?',
      'Save this for later and share with someone who needs it',
      'Try this and let me know how it goes',
      'What\'s your experience with this?',
      'Double tap if you agree',
      'Follow for more [topic] tips'
    ]
  }

  /**
   * Generate viral-optimized system prompt
   */
  static generateViralSystemPrompt(
    brandData: any,
    platform: string,
    contentGoal: string = 'viral'
  ): string {
    const platformRules = this.PLATFORM_RULES[platform] || this.PLATFORM_RULES.instagram
    
    return `You are ViralPath.ai's expert content strategist, specialized in creating viral-ready social media content that maintains professional brand standards.

## BRAND CONTEXT
- Brand: ${brandData.brandName}
- Industry: ${brandData.industry}
- Tone: ${brandData.tone}
- Target Audience: ${brandData.targetAudience}

## PLATFORM OPTIMIZATION (${platform.toUpperCase()})
- Caption Limit: ${platformRules.characterLimits.caption} characters
- Hashtag Strategy: ${platformRules.characterLimits.hashtags} character limit
- Peak Engagement Times: ${platformRules.peakTimes.join(', ')}
- Content Formats: ${platformRules.contentFormats.join(', ')}

## VIRAL CONTENT REQUIREMENTS
1. **Hook Mastery**: Use attention-grabbing opening lines from proven viral patterns
2. **Emotional Triggers**: Incorporate psychological triggers that drive sharing
3. **Platform Native**: Content must feel native to ${platform}, not repurposed
4. **Engagement Optimization**: Include elements that encourage comments, shares, saves
5. **Visual Synergy**: Ensure caption complements visual content perfectly
6. **Brand Consistency**: Maintain ${brandData.tone} tone while maximizing virality

## BEST PRACTICES FOR ${platform.toUpperCase()}
${platformRules.bestPractices.map(practice => `- ${practice}`).join('\n')}

## VIRAL ELEMENTS TO INTEGRATE
${platformRules.viralElements.map(element => `- ${element}`).join('\n')}

## OUTPUT REQUIREMENTS
Generate content that:
- Stops scroll with powerful hooks
- Drives meaningful engagement
- Encourages sharing and saves
- Maintains professional brand image
- Optimizes for ${platform} algorithm
- Includes strategic hashtag placement
- Provides clear visual direction

Always respond with valid JSON containing: caption, hashtags (array), and visualPrompt fields.`
  }

  /**
   * Generate viral-optimized user prompt
   */
  static generateViralUserPrompt(
    contentType: string,
    specificTopic: string,
    viralityGoal: number = 8,
    platform: string
  ): string {
    const randomHook = this.VIRAL_PATTERNS.hooks[Math.floor(Math.random() * this.VIRAL_PATTERNS.hooks.length)]
    const randomStructure = this.VIRAL_PATTERNS.structures[Math.floor(Math.random() * this.VIRAL_PATTERNS.structures.length)]
    const randomEnding = this.VIRAL_PATTERNS.endings[Math.floor(Math.random() * this.VIRAL_PATTERNS.endings.length)]
    
    return `Create viral-ready ${contentType} content about: ${specificTopic}

## VIRALITY TARGET
Target Virality Score: ${viralityGoal}/10 (where 10 = maximum viral potential)

## CONTENT STRATEGY
- Hook Pattern: "${randomHook}"
- Content Structure: ${randomStructure}
- Engagement Ending: "${randomEnding}"

## VIRAL OPTIMIZATION CHECKLIST
✅ Attention-grabbing hook (first 3 words crucial)
✅ Emotional trigger integration
✅ Platform-native formatting
✅ Clear value proposition
✅ Shareable moments creation
✅ Comment-driving questions
✅ Save-worthy information
✅ Algorithm-friendly elements

## SPECIFIC REQUIREMENTS
1. **Caption**: Must include the suggested hook pattern and ending
2. **Hashtags**: Mix of trending, niche, and branded hashtags
3. **Visual Prompt**: Detailed description for viral-optimized visual content

## ENGAGEMENT GOALS
- Comments: Include 2-3 discussion starters
- Shares: Create "must-share" moments
- Saves: Provide actionable/reference value
- Reach: Use trending elements and optimal hashtags

Generate content that people can't help but engage with while maintaining brand professionalism.`
  }

  /**
   * Get platform-specific viral elements
   */
  static getPlatformViralElements(platform: string): string[] {
    return this.PLATFORM_RULES[platform]?.viralElements || this.PLATFORM_RULES.instagram.viralElements
  }

  /**
   * Get viral hook suggestions
   */
  static getViralHooks(count: number = 5): string[] {
    const shuffled = [...this.VIRAL_PATTERNS.hooks].sort(() => 0.5 - Math.random())
    return shuffled.slice(0, count)
  }

  /**
   * Get content structure patterns
   */
  static getContentStructures(): string[] {
    return this.VIRAL_PATTERNS.structures
  }

  /**
   * Get engagement-driving endings
   */
  static getEngagementEndings(count: number = 3): string[] {
    const shuffled = [...this.VIRAL_PATTERNS.endings].sort(() => 0.5 - Math.random())
    return shuffled.slice(0, count)
  }

  /**
   * Analyze content for viral potential
   */
  static analyzeViralPotential(content: string, platform: string): {
    score: number
    strengths: string[]
    improvements: string[]
  } {
    const platformRules = this.PLATFORM_RULES[platform] || this.PLATFORM_RULES.instagram
    let score = 0
    const strengths: string[] = []
    const improvements: string[] = []

    // Check for viral hooks
    const hasViralHook = this.VIRAL_PATTERNS.hooks.some(hook => 
      content.toLowerCase().includes(hook.toLowerCase().split('...')[0])
    )
    if (hasViralHook) {
      score += 2
      strengths.push('Contains viral hook pattern')
    } else {
      improvements.push('Add attention-grabbing hook')
    }

    // Check for engagement endings
    const hasEngagementEnding = this.VIRAL_PATTERNS.endings.some(ending =>
      content.toLowerCase().includes(ending.toLowerCase().split(' ')[0])
    )
    if (hasEngagementEnding) {
      score += 1
      strengths.push('Includes engagement-driving ending')
    } else {
      improvements.push('Add call-to-action or engagement ending')
    }

    // Check character limits
    if (content.length <= platformRules.characterLimits.caption) {
      score += 1
      strengths.push('Optimal length for platform')
    } else {
      improvements.push(`Reduce length (current: ${content.length}, max: ${platformRules.characterLimits.caption})`)
    }

    // Check for questions
    const questionCount = (content.match(/\?/g) || []).length
    if (questionCount >= 1) {
      score += 1
      strengths.push('Includes engaging questions')
    } else {
      improvements.push('Add questions to drive comments')
    }

    // Check for emojis (visual appeal)
    const emojiCount = (content.match(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu) || []).length
    if (emojiCount >= 3) {
      score += 1
      strengths.push('Good emoji usage for visual appeal')
    } else {
      improvements.push('Add more emojis for visual engagement')
    }

    return {
      score: Math.min(score, 10),
      strengths,
      improvements
    }
  }

  /**
   * Get trending hashtag suggestions
   */
  static getTrendingHashtagSuggestions(industry: string, platform: string): string[] {
    const industryHashtags: Record<string, string[]> = {
      'e-commerce': ['#ecommerce', '#onlineshopping', '#retail', '#business', '#entrepreneur'],
      'technology': ['#tech', '#innovation', '#startup', '#ai', '#digital'],
      'health-wellness': ['#wellness', '#health', '#fitness', '#mindfulness', '#selfcare'],
      'food-beverage': ['#food', '#foodie', '#recipe', '#cooking', '#restaurant']
    }

    const platformTrending: Record<string, string[]> = {
      instagram: ['#reels', '#explore', '#viral', '#trending', '#instagood'],
      tiktok: ['#fyp', '#viral', '#trending', '#foryou', '#tiktok'],
      linkedin: ['#linkedin', '#professional', '#career', '#business', '#networking'],
      twitter: ['#twitter', '#trending', '#viral', '#breaking', '#news']
    }

    const industryTags = industryHashtags[industry.toLowerCase()] || industryHashtags['technology']
    const platformTags = platformTrending[platform] || platformTrending['instagram']

    return [...industryTags, ...platformTags]
  }
}