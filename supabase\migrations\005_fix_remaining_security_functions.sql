-- Fix Remaining Security Functions - Part 2
-- Continuation of security fixes for database functions

-- 7. Fix get_user_feature_flags function
DROP FUNCTION IF EXISTS public.get_user_feature_flags(UUID);
CREATE OR REPLACE FUNCTION public.get_user_feature_flags(p_user_id UUID)
RETURNS TABLE(flag_name TEXT, is_enabled BOOLEAN, config JSONB)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ff.flag_name,
        ff.is_enabled,
        ff.config
    FROM public.feature_flags ff
    WHERE ff.is_global = true
       OR ff.user_id = p_user_id
    ORDER BY ff.flag_name;
END;
$$;

-- 8. Fix audit_brands_changes function
DROP FUNCTION IF EXISTS public.audit_brands_changes();
CREATE OR REPLACE FUNCTION public.audit_brands_changes()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM public.log_audit_event(
            NEW.user_id,
            'create',
            'brand',
            jsonb_build_object('brand_id', NEW.id, 'brand_name', NEW.name)
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        PERFORM public.log_audit_event(
            NEW.user_id,
            'update',
            'brand',
            jsonb_build_object('brand_id', NEW.id, 'brand_name', NEW.name, 'changes', to_jsonb(NEW) - to_jsonb(OLD))
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM public.log_audit_event(
            OLD.user_id,
            'delete',
            'brand',
            jsonb_build_object('brand_id', OLD.id, 'brand_name', OLD.name)
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$;

-- 9. Fix handle_updated_at function
DROP FUNCTION IF EXISTS public.handle_updated_at();
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- 10. Fix ensure_single_active_brand function
DROP FUNCTION IF EXISTS public.ensure_single_active_brand();
CREATE OR REPLACE FUNCTION public.ensure_single_active_brand()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- If this brand is being set as active, deactivate all other brands for this user
    IF NEW.is_active = true THEN
        UPDATE public.brands 
        SET is_active = false, updated_at = NOW()
        WHERE user_id = NEW.user_id 
        AND id != NEW.id 
        AND is_active = true;
    END IF;
    
    RETURN NEW;
END;
$$;

-- 11. Fix create_content_version function
DROP FUNCTION IF EXISTS public.create_content_version(UUID, UUID, INTEGER, INTEGER, JSONB, TEXT);
CREATE OR REPLACE FUNCTION public.create_content_version(
    p_brand_id UUID,
    p_user_id UUID,
    p_month INTEGER,
    p_year INTEGER,
    p_content JSONB,
    p_version_notes TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    new_version_id UUID;
    next_version INTEGER;
BEGIN
    -- Get the next version number
    SELECT COALESCE(MAX(version), 0) + 1 INTO next_version
    FROM public.content_calendars
    WHERE brand_id = p_brand_id 
    AND user_id = p_user_id 
    AND month = p_month 
    AND year = p_year;
    
    -- Mark all existing versions as not current
    UPDATE public.content_calendars
    SET is_current = false, updated_at = NOW()
    WHERE brand_id = p_brand_id 
    AND user_id = p_user_id 
    AND month = p_month 
    AND year = p_year;
    
    -- Create new version
    INSERT INTO public.content_calendars (
        brand_id,
        user_id,
        month,
        year,
        content,
        version,
        is_current,
        version_notes,
        auto_saved
    ) VALUES (
        p_brand_id,
        p_user_id,
        p_month,
        p_year,
        p_content,
        next_version,
        true,
        p_version_notes,
        false
    ) RETURNING id INTO new_version_id;
    
    RETURN new_version_id;
END;
$$;

-- 12. Fix restore_content_version function
DROP FUNCTION IF EXISTS public.restore_content_version(UUID, UUID);
CREATE OR REPLACE FUNCTION public.restore_content_version(
    p_version_id UUID,
    p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    version_record RECORD;
BEGIN
    -- Get the version to restore
    SELECT brand_id, month, year, content, version
    INTO version_record
    FROM public.content_calendars
    WHERE id = p_version_id AND user_id = p_user_id;

    IF NOT FOUND THEN
        RETURN false;
    END IF;

    -- Create new version based on the restored content
    PERFORM public.create_content_version(
        version_record.brand_id,
        p_user_id,
        version_record.month,
        version_record.year,
        version_record.content,
        'Restored from version ' || version_record.version
    );

    RETURN true;
END;
$$;

-- 13. Fix get_content_version_history function
DROP FUNCTION IF EXISTS public.get_content_version_history(UUID, UUID, INTEGER, INTEGER);
CREATE OR REPLACE FUNCTION public.get_content_version_history(
    p_brand_id UUID,
    p_user_id UUID,
    p_month INTEGER,
    p_year INTEGER
)
RETURNS TABLE(
    id UUID,
    version INTEGER,
    version_notes TEXT,
    auto_saved BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    is_current BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT
        cc.id,
        cc.version,
        cc.version_notes,
        cc.auto_saved,
        cc.created_at,
        cc.is_current
    FROM public.content_calendars cc
    WHERE cc.brand_id = p_brand_id
    AND cc.user_id = p_user_id
    AND cc.month = p_month
    AND cc.year = p_year
    ORDER BY cc.version DESC;
END;
$$;

-- 14. Fix cleanup_old_versions function
DROP FUNCTION IF EXISTS public.cleanup_old_versions();
CREATE OR REPLACE FUNCTION public.cleanup_old_versions()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    deleted_count INTEGER := 0;
BEGIN
    -- Keep only the latest 10 versions per brand/month/year combination
    WITH versions_to_keep AS (
        SELECT id
        FROM (
            SELECT id,
                   ROW_NUMBER() OVER (
                       PARTITION BY brand_id, user_id, month, year
                       ORDER BY version DESC
                   ) as rn
            FROM public.content_calendars
        ) ranked
        WHERE rn <= 10
    )
    DELETE FROM public.content_calendars
    WHERE id NOT IN (SELECT id FROM versions_to_keep)
    AND created_at < NOW() - INTERVAL '30 days';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;

-- Add comments for documentation
COMMENT ON FUNCTION public.update_updated_at_column() IS 'Trigger function to update updated_at timestamp - SECURITY DEFINER';
COMMENT ON FUNCTION public.handle_new_user() IS 'Trigger function for new user registration - SECURITY DEFINER';
COMMENT ON FUNCTION public.log_audit_event(UUID, TEXT, TEXT, JSONB) IS 'Log audit events - SECURITY DEFINER';
COMMENT ON FUNCTION public.track_usage(UUID, TEXT, INTEGER) IS 'Track feature usage - SECURITY DEFINER';
COMMENT ON FUNCTION public.check_usage_limit(UUID, TEXT) IS 'Check usage limits - SECURITY DEFINER';
COMMENT ON FUNCTION public.create_notification(UUID, TEXT, TEXT, TEXT, JSONB) IS 'Create user notifications - SECURITY DEFINER';
COMMENT ON FUNCTION public.get_user_feature_flags(UUID) IS 'Get user feature flags - SECURITY DEFINER';
COMMENT ON FUNCTION public.audit_brands_changes() IS 'Audit brand changes trigger - SECURITY DEFINER';
COMMENT ON FUNCTION public.handle_updated_at() IS 'Handle updated_at trigger - SECURITY DEFINER';
COMMENT ON FUNCTION public.ensure_single_active_brand() IS 'Ensure single active brand trigger - SECURITY DEFINER';
COMMENT ON FUNCTION public.create_content_version(UUID, UUID, INTEGER, INTEGER, JSONB, TEXT) IS 'Create content version - SECURITY DEFINER';
COMMENT ON FUNCTION public.restore_content_version(UUID, UUID) IS 'Restore content version - SECURITY DEFINER';
COMMENT ON FUNCTION public.get_content_version_history(UUID, UUID, INTEGER, INTEGER) IS 'Get content version history - SECURITY DEFINER';
COMMENT ON FUNCTION public.cleanup_old_versions() IS 'Cleanup old content versions - SECURITY DEFINER';
