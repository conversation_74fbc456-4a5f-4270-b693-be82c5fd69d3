'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, Check, Zap, Crown, Sparkles } from 'lucide-react'
import toast from 'react-hot-toast'

interface ModelOption {
  id: string
  name: string
  displayName: string
  description: string
  tier: 'free' | 'paid'
  costPer1k: number
  features: string[]
  recommended?: boolean
}

const AVAILABLE_MODELS: ModelOption[] = [
  {
    id: 'openai/gpt-oss-20b:free',
    name: 'gpt-oss-20b',
    displayName: 'GPT OSS 20B (Free)',
    description: 'Free open-source model, great for basic content generation',
    tier: 'free',
    costPer1k: 0,
    features: ['Free usage', 'Basic content quality', 'Rate limited'],
    recommended: true
  },
  {
    id: 'openai/gpt-3.5-turbo',
    name: 'gpt-3.5-turbo',
    displayName: 'GPT-3.5 Turbo',
    description: 'Fast and efficient, excellent for most content needs',
    tier: 'paid',
    costPer1k: 0.0015,
    features: ['High speed', 'Good quality', 'Reliable']
  },
  {
    id: 'openai/gpt-4',
    name: 'gpt-4',
    displayName: 'GPT-4',
    description: 'Premium model with superior content quality and creativity',
    tier: 'paid',
    costPer1k: 0.03,
    features: ['Highest quality', 'Most creative', 'Best reasoning']
  },
  {
    id: 'anthropic/claude-3-haiku',
    name: 'claude-3-haiku',
    displayName: 'Claude 3 Haiku',
    description: 'Fast and cost-effective Claude model',
    tier: 'paid',
    costPer1k: 0.00025,
    features: ['Very fast', 'Cost effective', 'Good quality']
  },
  {
    id: 'anthropic/claude-3-sonnet',
    name: 'claude-3-sonnet',
    displayName: 'Claude 3 Sonnet',
    description: 'Balanced performance and quality from Anthropic',
    tier: 'paid',
    costPer1k: 0.003,
    features: ['Balanced', 'High quality', 'Reliable']
  },
  {
    id: 'deepseek/deepseek-chat-v3-0324:free',
    name: 'deepseek-chat-v3',
    displayName: 'DeepSeek V3 0324 (Free)',
    description: 'Free DeepSeek model with excellent reasoning capabilities',
    tier: 'free',
    costPer1k: 0,
    features: ['Free usage', 'Strong reasoning', 'Rate limited']
  },
  {
    id: 'microsoft/wizardlm-2-8x22b:free',
    name: 'wizardlm-2-8x22b',
    displayName: 'WizardLM 2 8x22B (Free)',
    description: 'Free Microsoft model with strong performance',
    tier: 'free',
    costPer1k: 0,
    features: ['Free usage', 'Good quality', 'Rate limited']
  },
  {
    id: 'google/gemma-2-9b-it:free',
    name: 'gemma-2-9b',
    displayName: 'Gemma 2 9B (Free)',
    description: 'Free Google model optimized for instruction following',
    tier: 'free',
    costPer1k: 0,
    features: ['Free usage', 'Instruction tuned', 'Rate limited']
  },
  {
    id: 'gemini-2.0-flash-exp',
    name: 'gemini-2.0-flash-exp',
    displayName: 'Gemini 2.0 Flash (Experimental)',
    description: 'Latest experimental Gemini model with enhanced speed and capabilities',
    tier: 'free',
    costPer1k: 0,
    features: ['Experimental', 'Very fast', 'Multimodal', 'Latest features']
  },
  {
    id: 'gemini-2.0-flash',
    name: 'gemini-2.0-flash',
    displayName: 'Gemini 2.0 Flash',
    description: 'Latest Google Gemini 2.0 model with enhanced multimodal capabilities and faster processing',
    tier: 'paid',
    costPer1k: 0.00015,
    features: ['Latest model', 'Enhanced multimodal', 'Ultra fast', 'Advanced reasoning']
  },
  {
    id: 'gemini-1.5-pro',
    name: 'gemini-1.5-pro',
    displayName: 'Gemini 1.5 Pro',
    description: 'Proven Google Gemini model with superior reasoning and creativity',
    tier: 'paid',
    costPer1k: 0.0035,
    features: ['High quality', 'Advanced reasoning', 'Multimodal', 'Stable']
  }
]

interface ModelSelectorProps {
  isOpen: boolean
  onClose: () => void
  currentModel: string
  onModelChange: (modelId: string) => void
}

export default function ModelSelector({ isOpen, onClose, currentModel, onModelChange }: ModelSelectorProps) {
  const [selectedModel, setSelectedModel] = useState(currentModel)
  const [filter, setFilter] = useState<'all' | 'free' | 'paid'>('all')

  useEffect(() => {
    setSelectedModel(currentModel)
  }, [currentModel])

  if (!isOpen) return null

  const filteredModels = AVAILABLE_MODELS.filter(model => {
    if (filter === 'all') return true
    return model.tier === filter
  })

  const handleSave = () => {
    onModelChange(selectedModel)
    toast.success('AI model updated successfully!')
    onClose()
  }

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'free':
        return <Zap className="w-4 h-4 text-green-500" />
      case 'paid':
        return <Crown className="w-4 h-4 text-purple-500" />
      default:
        return null
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-blue-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Settings className="w-6 h-6 text-purple-600" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900">AI Model Selection</h2>
                <p className="text-sm text-gray-600">Choose your preferred AI model for content generation</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="px-6 py-4 border-b border-gray-100">
          <div className="flex gap-2">
            {[
              { key: 'all', label: 'All Models', count: AVAILABLE_MODELS.length },
              { key: 'free', label: 'Free Models', count: AVAILABLE_MODELS.filter(m => m.tier === 'free').length },
              { key: 'paid', label: 'Premium Models', count: AVAILABLE_MODELS.filter(m => m.tier === 'paid').length }
            ].map(({ key, label, count }) => (
              <button
                key={key}
                onClick={() => setFilter(key as any)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${filter === key
                  ? 'bg-purple-100 text-purple-700 border border-purple-200'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
              >
                {label} ({count})
              </button>
            ))}
          </div>
        </div>

        {/* Models List */}
        <div className="px-6 py-4 max-h-96 overflow-y-auto">
          <div className="grid gap-4">
            {filteredModels.map((model) => (
              <div
                key={model.id}
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${selectedModel === model.id
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  } ${model.recommended ? 'ring-2 ring-green-200' : ''}`}
                onClick={() => setSelectedModel(model.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      {getTierIcon(model.tier)}
                      <h3 className="font-semibold text-gray-900">{model.displayName}</h3>
                      {model.recommended && (
                        <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full font-medium">
                          Recommended
                        </span>
                      )}
                      {selectedModel === model.id && (
                        <Check className="w-5 h-5 text-purple-600" />
                      )}
                    </div>
                    <p className="text-gray-600 text-sm mb-3">{model.description}</p>
                    <div className="flex items-center gap-4 text-sm">
                      <span className={`font-medium ${model.tier === 'free' ? 'text-green-600' : 'text-purple-600'}`}>
                        {model.tier === 'free' ? 'Free' : `$${model.costPer1k}/1K tokens`}
                      </span>
                      <div className="flex gap-2">
                        {model.features.map((feature, index) => (
                          <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              <Sparkles className="w-4 h-4 inline mr-1" />
              Premium models require OpenRouter credits
            </div>
            <div className="flex gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
