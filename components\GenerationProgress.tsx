'use client'

import { useState, useEffect } from 'react'
import { Sparkles, Calendar, Hash, Image, Clock } from 'lucide-react'
import { ProgressBar } from './SkeletonLoader'

interface GenerationProgressProps {
  isGenerating: boolean
  onComplete?: () => void
}

const generationSteps = [
  {
    id: 'analyzing',
    label: 'Analyzing your brand',
    icon: <Sparkles className="w-5 h-5" />,
    duration: 2000
  },
  {
    id: 'generating',
    label: 'Generating content ideas',
    icon: <Calendar className="w-5 h-5" />,
    duration: 3000
  },
  {
    id: 'hashtags',
    label: 'Creating hashtags',
    icon: <Hash className="w-5 h-5" />,
    duration: 2000
  },
  {
    id: 'visuals',
    label: 'Generating visual prompts',
    icon: <Image className="w-5 h-5" />,
    duration: 2500
  },
  {
    id: 'timing',
    label: 'Optimizing posting times',
    icon: <Clock className="w-5 h-5" />,
    duration: 1500
  }
]

export default function GenerationProgress({ isGenerating, onComplete }: GenerationProgressProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [progress, setProgress] = useState(0)
  const [stepProgress, setStepProgress] = useState(0)

  useEffect(() => {
    if (!isGenerating) {
      setCurrentStep(0)
      setProgress(0)
      setStepProgress(0)
      return
    }

    let stepIndex = 0
    let totalElapsed = 0
    const totalDuration = generationSteps.reduce((sum, step) => sum + step.duration, 0)

    const runStep = () => {
      if (stepIndex >= generationSteps.length) {
        setProgress(100)
        setStepProgress(100)
        onComplete?.()
        return
      }

      const step = generationSteps[stepIndex]
      setCurrentStep(stepIndex)
      setStepProgress(0)

      const stepStartTime = Date.now()
      const stepInterval = setInterval(() => {
        const elapsed = Date.now() - stepStartTime
        const stepProg = Math.min(100, (elapsed / step.duration) * 100)
        setStepProgress(stepProg)

        const overallProg = ((totalElapsed + elapsed) / totalDuration) * 100
        setProgress(Math.min(100, overallProg))

        if (elapsed >= step.duration) {
          clearInterval(stepInterval)
          totalElapsed += step.duration
          stepIndex++
          setTimeout(runStep, 100) // Small delay between steps
        }
      }, 50)
    }

    runStep()
  }, [isGenerating, onComplete])

  if (!isGenerating) {
    return null
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
            <Sparkles className="w-8 h-8 text-white animate-pulse" />
          </div>
          <h2 className="text-2xl font-heading font-bold text-neutral-dark mb-2">
            Creating Your Calendar
          </h2>
          <p className="text-gray-600">
            Our AI is crafting personalized content for your brand
          </p>
        </div>

        {/* Overall Progress */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Overall Progress</span>
            <span className="text-sm text-gray-500">{Math.round(progress)}%</span>
          </div>
          <ProgressBar progress={progress} />
        </div>

        {/* Current Step */}
        <div className="space-y-4">
          {generationSteps.map((step, index) => {
            const isActive = index === currentStep
            const isCompleted = index < currentStep
            const isUpcoming = index > currentStep

            return (
              <div
                key={step.id}
                className={`flex items-center gap-3 p-3 rounded-lg transition-all duration-300 ${
                  isActive
                    ? 'bg-primary-purple/10 border border-primary-purple/20'
                    : isCompleted
                    ? 'bg-green-50 border border-green-200'
                    : 'bg-gray-50 border border-gray-200'
                }`}
              >
                <div
                  className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-300 ${
                    isActive
                      ? 'bg-primary-purple text-white'
                      : isCompleted
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-300 text-gray-600'
                  }`}
                >
                  {isCompleted ? (
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    step.icon
                  )}
                </div>

                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span
                      className={`font-medium transition-colors duration-300 ${
                        isActive
                          ? 'text-primary-purple'
                          : isCompleted
                          ? 'text-green-700'
                          : 'text-gray-600'
                      }`}
                    >
                      {step.label}
                    </span>
                    {isActive && (
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-purple"></div>
                        <span className="text-xs text-primary-purple">
                          {Math.round(stepProgress)}%
                        </span>
                      </div>
                    )}
                  </div>

                  {isActive && (
                    <div className="mt-2">
                      <ProgressBar progress={stepProgress} className="h-1" />
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>

        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            This usually takes 10-15 seconds
          </p>
        </div>
      </div>
    </div>
  )
}
