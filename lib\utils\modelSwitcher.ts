// Model Switching Utility
// Handles automatic model switching when rate limits are hit

import { ModelSettingsService } from '@/lib/services/modelSettingsService'

export class ModelSwitcher {
  private static rateLimitedModels: Set<string> = new Set()
  private static lastRateLimitTime: Map<string, number> = new Map()

  // Rate limit cooldown period (1 minute for Gemini free tier)
  private static readonly RATE_LIMIT_COOLDOWN = 60 * 1000 // 1 minute in milliseconds

  /**
   * Mark a model as rate limited
   */
  static markModelAsRateLimited(modelId: string): void {
    console.log(`🚫 Marking ${modelId} as rate limited`)
    this.rateLimitedModels.add(modelId)
    this.lastRateLimitTime.set(modelId, Date.now())
  }

  /**
   * Check if a model is currently rate limited
   */
  static isModelRateLimited(modelId: string): boolean {
    if (!this.rateLimitedModels.has(modelId)) {
      return false
    }

    const lastRateLimit = this.lastRateLimitTime.get(modelId)
    if (!lastRateLimit) {
      return false
    }

    // Check if cooldown period has passed
    const timeSinceRateLimit = Date.now() - lastRateLimit
    if (timeSinceRateLimit > this.RATE_LIMIT_COOLDOWN) {
      console.log(`✅ ${modelId} rate limit cooldown expired, removing from rate limited list`)
      this.rateLimitedModels.delete(modelId)
      this.lastRateLimitTime.delete(modelId)
      return false
    }

    return true
  }

  /**
   * Get the best available model (not rate limited)
   */
  static getBestAvailableModel(): string {
    const currentModel = ModelSettingsService.getCurrentModel()

    // If current model is not rate limited, use it
    if (!this.isModelRateLimited(currentModel)) {
      return currentModel
    }

    console.log(`⚠️ Current model ${currentModel} is rate limited, finding alternative...`)

    // List of fallback models in order of preference
    const fallbackModels = [
      'openai/gpt-oss-20b:free',           // Free, reliable
      'deepseek/deepseek-chat-v3-0324:free', // Free, good quality
      'microsoft/wizardlm-2-8x22b:free',   // Free, decent
      'google/gemma-2-9b-it:free',         // Free, basic
      'openai/gpt-3.5-turbo',              // Paid, fast
      'anthropic/claude-3-haiku',          // Paid, cost-effective
      'gemini-2.0-flash',                  // Paid, latest & fast
      'gemini-1.5-flash',                  // Paid, fast
      'anthropic/claude-3-sonnet',         // Paid, high quality
      'gemini-1.5-pro',                   // Paid, premium
      'openai/gpt-4'                       // Paid, highest quality
    ]

    // Find first available model
    for (const model of fallbackModels) {
      if (!this.isModelRateLimited(model)) {
        console.log(`🔄 Switching to available model: ${model}`)
        ModelSettingsService.updateSelectedModel(model)
        return model
      }
    }

    // If all models are rate limited (unlikely), return the original
    console.warn('⚠️ All models appear to be rate limited, using original model')
    return currentModel
  }

  /**
   * Handle rate limit error and switch models
   */
  static handleRateLimitError(error: Error, currentModel: string): string {
    // Check if it's a rate limit error
    const isRateLimit = error.message.includes('429') ||
      error.message.includes('Rate limit') ||
      error.message.includes('quota') ||
      error.message.includes('RESOURCE_EXHAUSTED')

    if (!isRateLimit) {
      throw error // Re-throw if not a rate limit error
    }

    console.warn(`⚠️ Rate limit hit for ${currentModel}:`, error.message)

    // Mark current model as rate limited
    this.markModelAsRateLimited(currentModel)

    // Get best available alternative
    const newModel = this.getBestAvailableModel()

    if (newModel === currentModel) {
      // No alternative found, throw user-friendly error
      const cooldownMinutes = Math.ceil(this.RATE_LIMIT_COOLDOWN / 60000)
      throw new Error(`${ModelSettingsService.getModelInfo(currentModel).name} rate limit exceeded. Please wait ${cooldownMinutes} minute(s) or try again with a different model.`)
    }

    return newModel
  }

  /**
   * Get rate limit status for all models
   */
  static getRateLimitStatus(): Record<string, { isRateLimited: boolean; timeRemaining?: number }> {
    const allModels = [
      'openai/gpt-oss-20b:free',
      'deepseek/deepseek-chat-v3-0324:free',
      'microsoft/wizardlm-2-8x22b:free',
      'google/gemma-2-9b-it:free',
      'gemini-2.0-flash',
      'gemini-1.5-flash',
      'gemini-1.5-pro',
      'openai/gpt-3.5-turbo',
      'openai/gpt-4',
      'anthropic/claude-3-haiku',
      'anthropic/claude-3-sonnet'
    ]

    const status: Record<string, { isRateLimited: boolean; timeRemaining?: number }> = {}

    for (const model of allModels) {
      const isRateLimited = this.isModelRateLimited(model)
      const lastRateLimit = this.lastRateLimitTime.get(model)

      status[model] = {
        isRateLimited,
        timeRemaining: isRateLimited && lastRateLimit
          ? Math.max(0, this.RATE_LIMIT_COOLDOWN - (Date.now() - lastRateLimit))
          : undefined
      }
    }

    return status
  }

  /**
   * Clear all rate limit records (for testing/debugging)
   */
  static clearAllRateLimits(): void {
    console.log('🧹 Clearing all rate limit records')
    this.rateLimitedModels.clear()
    this.lastRateLimitTime.clear()
  }
}
