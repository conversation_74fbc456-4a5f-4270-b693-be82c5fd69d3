-- Fix Function Signature Conflicts - Final Corrected Version
-- This migration handles functions with different return type signatures

-- First, let's check what functions exist and drop only those with signature conflicts
-- We'll use CASCADE only where necessary and be very specific

-- 1. Fix get_user_feature_flags function (has return type conflict)
-- Drop with CASCAD<PERSON> since we need to change the return type
DROP FUNCTION IF EXISTS public.get_user_feature_flags(UUID) CASCADE;

-- Recreate with correct signature and security settings
CREATE OR REPLACE FUNCTION public.get_user_feature_flags(p_user_id UUID)
RETURNS TABLE(flag_name TEXT, is_enabled BOOLEAN, config JSONB)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ff.flag_name,
        ff.is_enabled,
        ff.config
    FROM public.feature_flags ff
    WHERE ff.is_global = true
       OR ff.user_id = p_user_id
    ORDER BY ff.flag_name;
END;
$$;

-- 2. Check and fix other functions that might have signature conflicts
-- Let's be more careful with parameter signatures

-- Fix log_audit_event - check if it exists with different signature
DROP FUNCTION IF EXISTS public.log_audit_event(UUID, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.log_audit_event(UUID, TEXT, TEXT, JSONB) CASCADE;

CREATE OR REPLACE FUNCTION public.log_audit_event(
    p_user_id UUID,
    p_action TEXT,
    p_resource_type TEXT,
    p_details JSONB DEFAULT '{}'::jsonb
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    INSERT INTO public.audit_logs (
        user_id,
        action,
        resource_type,
        details,
        ip_address,
        user_agent
    ) VALUES (
        p_user_id,
        p_action,
        p_resource_type,
        p_details,
        COALESCE(current_setting('request.headers', true)::json->>'x-forwarded-for', 'unknown'),
        COALESCE(current_setting('request.headers', true)::json->>'user-agent', 'unknown')
    );
END;
$$;

-- 3. Fix track_usage function - check for signature conflicts
DROP FUNCTION IF EXISTS public.track_usage(UUID, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.track_usage(UUID, TEXT, INTEGER) CASCADE;

CREATE OR REPLACE FUNCTION public.track_usage(
    p_user_id UUID,
    p_feature TEXT,
    p_usage_count INTEGER DEFAULT 1
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    INSERT INTO public.usage_tracking (
        user_id,
        feature,
        usage_count,
        date
    ) VALUES (
        p_user_id,
        p_feature,
        p_usage_count,
        CURRENT_DATE
    )
    ON CONFLICT (user_id, feature, date)
    DO UPDATE SET
        usage_count = public.usage_tracking.usage_count + p_usage_count,
        updated_at = NOW();
END;
$$;

-- 4. Fix check_usage_limit function
DROP FUNCTION IF EXISTS public.check_usage_limit(UUID, TEXT) CASCADE;

CREATE OR REPLACE FUNCTION public.check_usage_limit(
    p_user_id UUID,
    p_feature TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    current_usage INTEGER;
    user_tier TEXT;
    tier_limit INTEGER;
BEGIN
    -- Get user's subscription tier
    SELECT s.tier INTO user_tier
    FROM public.subscriptions s
    WHERE s.user_id = p_user_id AND s.status = 'active'
    ORDER BY s.created_at DESC
    LIMIT 1;
    
    -- Default to free tier if no subscription found
    user_tier := COALESCE(user_tier, 'free');
    
    -- Get current month usage
    SELECT COALESCE(SUM(usage_count), 0) INTO current_usage
    FROM public.usage_tracking
    WHERE user_id = p_user_id 
    AND feature = p_feature 
    AND date >= date_trunc('month', CURRENT_DATE);
    
    -- Set limits based on tier and feature
    CASE 
        WHEN user_tier = 'free' AND p_feature = 'calendar_generation' THEN tier_limit := 3;
        WHEN user_tier = 'starter' AND p_feature = 'calendar_generation' THEN tier_limit := 10;
        WHEN user_tier = 'professional' AND p_feature = 'calendar_generation' THEN tier_limit := 50;
        WHEN user_tier = 'enterprise' THEN tier_limit := 999999; -- Unlimited
        ELSE tier_limit := 1; -- Default conservative limit
    END CASE;
    
    RETURN current_usage < tier_limit;
END;
$$;

-- 5. Fix create_notification function - check for signature conflicts
DROP FUNCTION IF EXISTS public.create_notification(UUID, TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.create_notification(UUID, TEXT, TEXT, TEXT, JSONB) CASCADE;

CREATE OR REPLACE FUNCTION public.create_notification(
    p_user_id UUID,
    p_type TEXT,
    p_title TEXT,
    p_message TEXT,
    p_metadata JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO public.notifications (
        user_id,
        type,
        title,
        message,
        metadata,
        is_read
    ) VALUES (
        p_user_id,
        p_type,
        p_title,
        p_message,
        p_metadata,
        false
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$;

-- 6. Fix content versioning functions - check for signature conflicts
DROP FUNCTION IF EXISTS public.create_content_version(UUID, UUID, INTEGER, INTEGER, JSONB) CASCADE;
DROP FUNCTION IF EXISTS public.create_content_version(UUID, UUID, INTEGER, INTEGER, JSONB, TEXT) CASCADE;

CREATE OR REPLACE FUNCTION public.create_content_version(
    p_brand_id UUID,
    p_user_id UUID,
    p_month INTEGER,
    p_year INTEGER,
    p_content JSONB,
    p_version_notes TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    new_version_id UUID;
    next_version INTEGER;
BEGIN
    -- Get the next version number
    SELECT COALESCE(MAX(version), 0) + 1 INTO next_version
    FROM public.content_calendars
    WHERE brand_id = p_brand_id
    AND user_id = p_user_id
    AND month = p_month
    AND year = p_year;

    -- Mark all existing versions as not current
    UPDATE public.content_calendars
    SET is_current = false, updated_at = NOW()
    WHERE brand_id = p_brand_id
    AND user_id = p_user_id
    AND month = p_month
    AND year = p_year;

    -- Create new version
    INSERT INTO public.content_calendars (
        brand_id,
        user_id,
        month,
        year,
        content,
        version,
        is_current,
        version_notes,
        auto_saved
    ) VALUES (
        p_brand_id,
        p_user_id,
        p_month,
        p_year,
        p_content,
        next_version,
        true,
        p_version_notes,
        false
    ) RETURNING id INTO new_version_id;

    RETURN new_version_id;
END;
$$;

-- 7. Fix restore_content_version function
DROP FUNCTION IF EXISTS public.restore_content_version(UUID, UUID) CASCADE;

CREATE OR REPLACE FUNCTION public.restore_content_version(
    p_version_id UUID,
    p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    version_record RECORD;
BEGIN
    -- Get the version to restore
    SELECT brand_id, month, year, content, version
    INTO version_record
    FROM public.content_calendars
    WHERE id = p_version_id AND user_id = p_user_id;

    IF NOT FOUND THEN
        RETURN false;
    END IF;

    -- Create new version based on the restored content
    PERFORM public.create_content_version(
        version_record.brand_id,
        p_user_id,
        version_record.month,
        version_record.year,
        version_record.content,
        'Restored from version ' || version_record.version
    );

    RETURN true;
END;
$$;

-- 8. Fix get_content_version_history function
DROP FUNCTION IF EXISTS public.get_content_version_history(UUID, UUID, INTEGER, INTEGER) CASCADE;

CREATE OR REPLACE FUNCTION public.get_content_version_history(
    p_brand_id UUID,
    p_user_id UUID,
    p_month INTEGER,
    p_year INTEGER
)
RETURNS TABLE(
    id UUID,
    version INTEGER,
    version_notes TEXT,
    auto_saved BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    is_current BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT
        cc.id,
        cc.version,
        cc.version_notes,
        cc.auto_saved,
        cc.created_at,
        cc.is_current
    FROM public.content_calendars cc
    WHERE cc.brand_id = p_brand_id
    AND cc.user_id = p_user_id
    AND cc.month = p_month
    AND cc.year = p_year
    ORDER BY cc.version DESC;
END;
$$;

-- 9. Fix cleanup_old_versions function
DROP FUNCTION IF EXISTS public.cleanup_old_versions() CASCADE;

CREATE OR REPLACE FUNCTION public.cleanup_old_versions()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    deleted_count INTEGER := 0;
BEGIN
    -- Keep only the latest 10 versions per brand/month/year combination
    WITH versions_to_keep AS (
        SELECT id
        FROM (
            SELECT id,
                   ROW_NUMBER() OVER (
                       PARTITION BY brand_id, user_id, month, year
                       ORDER BY version DESC
                   ) as rn
            FROM public.content_calendars
        ) ranked
        WHERE rn <= 10
    )
    DELETE FROM public.content_calendars
    WHERE id NOT IN (SELECT id FROM versions_to_keep)
    AND created_at < NOW() - INTERVAL '30 days';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;

-- Now fix the trigger functions that can use CREATE OR REPLACE safely
-- These don't have signature conflicts, just need security updates

-- 10. Fix update_updated_at_column (trigger function)
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- 11. Fix handle_new_user (trigger function)
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    INSERT INTO public.user_profiles (
        id,
        email,
        full_name,
        avatar_url,
        timezone,
        language,
        onboarding_completed,
        email_notifications,
        marketing_emails
    ) VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'avatar_url', ''),
        COALESCE(NEW.raw_user_meta_data->>'timezone', 'UTC'),
        COALESCE(NEW.raw_user_meta_data->>'language', 'en'),
        false,
        true,
        false
    );
    RETURN NEW;
END;
$$;

-- 12. Fix audit_brands_changes (trigger function)
CREATE OR REPLACE FUNCTION public.audit_brands_changes()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM public.log_audit_event(
            NEW.user_id,
            'create',
            'brand',
            jsonb_build_object('brand_id', NEW.id, 'brand_name', NEW.name)
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        PERFORM public.log_audit_event(
            NEW.user_id,
            'update',
            'brand',
            jsonb_build_object('brand_id', NEW.id, 'brand_name', NEW.name, 'changes', to_jsonb(NEW) - to_jsonb(OLD))
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM public.log_audit_event(
            OLD.user_id,
            'delete',
            'brand',
            jsonb_build_object('brand_id', OLD.id, 'brand_name', OLD.name)
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$;

-- 13. Fix handle_updated_at (trigger function)
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- 14. Fix ensure_single_active_brand (trigger function)
CREATE OR REPLACE FUNCTION public.ensure_single_active_brand()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- If this brand is being set as active, deactivate all other brands for this user
    IF NEW.is_active = true THEN
        UPDATE public.brands
        SET is_active = false, updated_at = NOW()
        WHERE user_id = NEW.user_id
        AND id != NEW.id
        AND is_active = true;
    END IF;

    RETURN NEW;
END;
$$;

-- Add security comments for all functions
COMMENT ON FUNCTION public.get_user_feature_flags(UUID) IS 'Get user feature flags - SECURITY DEFINER with fixed search_path';
COMMENT ON FUNCTION public.log_audit_event(UUID, TEXT, TEXT, JSONB) IS 'Log audit events - SECURITY DEFINER with fixed search_path';
COMMENT ON FUNCTION public.track_usage(UUID, TEXT, INTEGER) IS 'Track feature usage - SECURITY DEFINER with fixed search_path';
COMMENT ON FUNCTION public.check_usage_limit(UUID, TEXT) IS 'Check usage limits - SECURITY DEFINER with fixed search_path';
COMMENT ON FUNCTION public.create_notification(UUID, TEXT, TEXT, TEXT, JSONB) IS 'Create user notifications - SECURITY DEFINER with fixed search_path';
COMMENT ON FUNCTION public.create_content_version(UUID, UUID, INTEGER, INTEGER, JSONB, TEXT) IS 'Create content version - SECURITY DEFINER with fixed search_path';
COMMENT ON FUNCTION public.restore_content_version(UUID, UUID) IS 'Restore content version - SECURITY DEFINER with fixed search_path';
COMMENT ON FUNCTION public.get_content_version_history(UUID, UUID, INTEGER, INTEGER) IS 'Get content version history - SECURITY DEFINER with fixed search_path';
COMMENT ON FUNCTION public.cleanup_old_versions() IS 'Cleanup old content versions - SECURITY DEFINER with fixed search_path';
COMMENT ON FUNCTION public.update_updated_at_column() IS 'Trigger function to update updated_at timestamp - SECURITY DEFINER with fixed search_path';
COMMENT ON FUNCTION public.handle_new_user() IS 'Trigger function for new user registration - SECURITY DEFINER with fixed search_path';
COMMENT ON FUNCTION public.audit_brands_changes() IS 'Audit brand changes trigger - SECURITY DEFINER with fixed search_path';
COMMENT ON FUNCTION public.handle_updated_at() IS 'Handle updated_at trigger - SECURITY DEFINER with fixed search_path';
COMMENT ON FUNCTION public.ensure_single_active_brand() IS 'Ensure single active brand trigger - SECURITY DEFINER with fixed search_path';

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'All function signature conflicts resolved - 14 functions now use SECURITY DEFINER with fixed search_path';
END $$;
