// Google Gemini AI Service
// Handles API calls to Google's Gemini models

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string
      }>
    }
  }>
}

interface GeminiRequest {
  contents: Array<{
    parts: Array<{
      text: string
    }>
  }>
  generationConfig: {
    temperature: number
    maxOutputTokens: number
    topP: number
  }
}

export class GeminiService {
  private static readonly API_KEY = 'AIzaSyCQVTCG2n-7PuiSb12BI7SMW_ct1ov3ZIE'
  private static readonly BASE_URL = 'https://generativelanguage.googleapis.com/v1beta/models'

  /**
   * Generate content using Gemini model
   */
  static async generateContent(
    model: string,
    prompt: string,
    settings: {
      temperature: number
      max_tokens: number
      top_p: number
    }
  ): Promise<string> {
    try {
      console.log(`🤖 Calling Google Gemini API with ${model}...`)

      const requestBody: GeminiRequest = {
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: settings.temperature,
          maxOutputTokens: settings.max_tokens,
          topP: settings.top_p
        }
      }

      const response = await fetch(
        `${this.BASE_URL}/${model}:generateContent?key=${this.API_KEY}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody)
        }
      )

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Gemini API error:', response.status, errorText)
        throw new Error(`Gemini API error: ${response.status} ${errorText}`)
      }

      const data: GeminiResponse = await response.json()
      console.log('✅ Gemini API call successful')

      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('No response from Gemini API')
      }

      const content = data.candidates[0]?.content?.parts?.[0]?.text
      if (!content) {
        throw new Error('Invalid response format from Gemini API')
      }

      return content

    } catch (error) {
      console.error('Gemini generation error:', error)
      throw error
    }
  }

  /**
   * Check if model is a Gemini model
   */
  static isGeminiModel(modelId: string): boolean {
    return modelId.startsWith('gemini-')
  }

  /**
   * Get Gemini model display name
   */
  static getGeminiModelName(modelId: string): string {
    const modelNames: Record<string, string> = {
      // Latest Gemini 2.5 models (2025)
      'gemini-2.5-pro': 'Gemini 2.5 Pro',
      'gemini-2.5-flash': 'Gemini 2.5 Flash',
      'gemini-2.5-flash-lite': 'Gemini 2.5 Flash-Lite',
      // Gemini 2.0 models
      'gemini-2.0-flash': 'Gemini 2.0 Flash',
      'gemini-2.0-flash-lite': 'Gemini 2.0 Flash-Lite',
      'gemini-2.0-flash-preview-image-generation': 'Gemini 2.0 Flash (Image Gen)',
      // Legacy models (deprecated)
      'gemini-1.5-flash': 'Gemini 1.5 Flash (Legacy)',
      'gemini-1.5-pro': 'Gemini 1.5 Pro (Legacy)',
      'gemini-pro': 'Gemini Pro (Legacy)',
      'gemini-pro-vision': 'Gemini Pro Vision (Legacy)'
    }
    return modelNames[modelId] || modelId
  }

  /**
   * Get recommended settings for Gemini models
   */
  static getGeminiSettings(modelId: string) {
    const settingsMap: Record<string, any> = {
      // Latest Gemini 2.5 models - optimized for viral content
      'gemini-2.5-pro': {
        temperature: 0.8, // Higher creativity for viral content
        max_tokens: 4000,
        top_p: 0.9
      },
      'gemini-2.5-flash': {
        temperature: 0.75,
        max_tokens: 3000,
        top_p: 0.9
      },
      'gemini-2.5-flash-lite': {
        temperature: 0.7,
        max_tokens: 2000,
        top_p: 0.95
      },
      // Gemini 2.0 models
      'gemini-2.0-flash': {
        temperature: 0.75,
        max_tokens: 3000,
        top_p: 0.9
      },
      'gemini-2.0-flash-lite': {
        temperature: 0.7,
        max_tokens: 2000,
        top_p: 0.95
      },
      // Legacy models (deprecated)
      'gemini-1.5-flash': {
        temperature: 0.7,
        max_tokens: 1500,
        top_p: 0.95
      },
      'gemini-1.5-pro': {
        temperature: 0.7,
        max_tokens: 2000,
        top_p: 0.9
      },
      'gemini-pro': {
        temperature: 0.7,
        max_tokens: 1500,
        top_p: 0.95
      }
    }

    return settingsMap[modelId] || {
      temperature: 0.75,
      max_tokens: 2500,
      top_p: 0.9
    }
  }

  /**
   * Validate Gemini API key
   */
  static async validateApiKey(): Promise<boolean> {
    try {
      const response = await fetch(
        `${this.BASE_URL}/gemini-1.5-flash:generateContent?key=${this.API_KEY}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [
              {
                parts: [
                  {
                    text: 'Hello'
                  }
                ]
              }
            ],
            generationConfig: {
              temperature: 0.7,
              maxOutputTokens: 10,
              topP: 0.95
            }
          })
        }
      )

      return response.ok || response.status === 400 // 400 might be quota exceeded but key is valid
    } catch (error) {
      console.error('Gemini API key validation error:', error)
      return false
    }
  }

  /**
   * Format prompt for Gemini models
   */
  static formatPromptForGemini(systemPrompt: string, userPrompt: string): string {
    return `${systemPrompt}\n\nUser Request: ${userPrompt}\n\nPlease respond with valid JSON only, no markdown formatting or additional text.`
  }
}
