import { createClient } from '@/lib/supabase/client'
import type { Database } from '@/lib/supabase/database.types'
import { safeAsyncOperation, createApiError, withRetry } from '@/lib/utils/errorHandling'

type ContentCalendar = Database['public']['Tables']['content_calendars']['Row']
type ContentCalendarInsert = Database['public']['Tables']['content_calendars']['Insert']
type ContentCalendarUpdate = Database['public']['Tables']['content_calendars']['Update']

export interface ContentItem {
  id: string
  date: string
  platform: string
  type: string
  caption: string
  hashtags: string[]
  visualPrompt: string
  bestTime: string
}

export interface ContentVersion {
  id: string
  version: number
  is_current: boolean
  created_at: string
  version_notes?: string
  auto_saved: boolean
  content_preview: string
}

class ContentCalendarService {
  private supabase = createClient()

  /**
   * Save calendar content to database with versioning support
   */
  async saveCalendarContent(
    brandId: string,
    userId: string,
    month: number,
    year: number,
    content: ContentItem[],
    options: {
      versionNotes?: string
      autoSaved?: boolean
    } = {}
  ): Promise<ContentCalendar | null> {
    return await safeAsyncOperation(
      async () => {
        // Check authentication first
        const { data: { user }, error: authError } = await this.supabase.auth.getUser()
        if (authError || !user) {
          console.warn('Authentication issue in saveCalendarContent:', authError)
          throw createApiError(
            'Authentication required to save calendar content',
            401,
            'AUTH_REQUIRED',
            authError
          )
        }

        const calendarData: ContentCalendarInsert = {
          brand_id: brandId,
          user_id: userId,
          month,
          year,
          content: content as any,
          version_notes: options.versionNotes,
          auto_saved: options.autoSaved || false,
          is_current: true
        }

        console.log('Saving calendar content for:', { brandId, userId, month, year, contentLength: content.length })

        // Use retry logic for database operations
        return await withRetry(async () => {
          const { data, error } = await this.supabase
            .from('content_calendars')
            .upsert(calendarData, {
              onConflict: 'brand_id,month,year,is_current'
            })
            .select()
            .single()

          if (error) {
            console.error('Calendar content save error:', {
              code: error.code,
              message: error.message,
              details: error.details,
              hint: error.hint,
              data: calendarData
            })

            // Handle 406 errors specifically
            if (error.message.includes('406') || error.code === 'PGRST406') {
              throw createApiError(
                'Content format not acceptable. Please check your authentication and try again.',
                406,
                'CONTENT_NOT_ACCEPTABLE',
                error
              )
            }

            throw createApiError(
              `Failed to save calendar content: ${error.message}`,
              error.code === 'PGRST301' ? 400 : 500,
              error.code,
              error
            )
          }

          return data
        }, { maxRetries: 2, delay: 1000 })
      },
      {
        context: 'saveCalendarContent',
        showErrorToast: false // Let the calling function handle the toast
      }
    )
  }

  /**
   * Load calendar content from database
   */
  async loadCalendarContent(
    brandId: string,
    userId: string,
    month: number,
    year: number
  ): Promise<ContentItem[]> {
    const result = await safeAsyncOperation(
      async () => {
        return await withRetry(async () => {
          // Check authentication first
          const { data: { user }, error: authError } = await this.supabase.auth.getUser()
          if (authError || !user) {
            console.warn('Authentication issue in loadCalendarContent:', authError)
            throw createApiError(
              'Authentication required to load calendar content',
              401,
              'AUTH_REQUIRED',
              authError
            )
          }

          console.log('Loading calendar content for:', { brandId, userId, month, year, authenticatedUser: user.id })

          const { data, error } = await this.supabase
            .from('content_calendars')
            .select('*')
            .eq('brand_id', brandId)
            .eq('user_id', userId)
            .eq('month', month)
            .eq('year', year)
            .eq('is_current', true)
            .single()

          if (error) {
            console.error('Calendar content query error:', {
              code: error.code,
              message: error.message,
              details: error.details,
              hint: error.hint,
              query: { brandId, userId, month, year }
            })

            if (error.code === 'PGRST116') {
              // No data found, return empty array
              return []
            }

            // Handle 406 errors specifically
            if (error.message.includes('406') || error.code === 'PGRST406') {
              throw createApiError(
                'Content format not acceptable. Please check your authentication and try again.',
                406,
                'CONTENT_NOT_ACCEPTABLE',
                error
              )
            }

            throw createApiError(
              `Failed to load calendar content: ${error.message}`,
              error.code === 'PGRST301' ? 400 : 500,
              error.code,
              error
            )
          }

          return (data.content as ContentItem[]) || []
        }, { maxRetries: 2, delay: 500 })
      },
      {
        context: 'loadCalendarContent',
        showErrorToast: false,
        fallbackValue: []
      }
    )

    return result || []
  }

  /**
   * Update specific content item in calendar
   */
  async updateContentItem(
    brandId: string,
    userId: string,
    month: number,
    year: number,
    updatedItem: ContentItem
  ): Promise<boolean> {
    try {
      // First, load existing content
      const existingContent = await this.loadCalendarContent(brandId, userId, month, year)

      // Update the specific item
      const updatedContent = existingContent.map(item =>
        item.id === updatedItem.id ? updatedItem : item
      )

      // Save back to database
      const result = await this.saveCalendarContent(brandId, userId, month, year, updatedContent)
      return result !== null
    } catch (error) {
      console.error('Error in updateContentItem:', error)
      return false
    }
  }

  /**
   * Delete calendar content for a specific month/year
   */
  async deleteCalendarContent(
    brandId: string,
    userId: string,
    month: number,
    year: number
  ): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('content_calendars')
        .delete()
        .eq('brand_id', brandId)
        .eq('user_id', userId)
        .eq('month', month)
        .eq('year', year)

      if (error) {
        console.error('Error deleting calendar content:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error in deleteCalendarContent:', error)
      return false
    }
  }

  /**
   * Get all calendar content for a brand
   */
  async getBrandCalendars(brandId: string, userId: string): Promise<ContentCalendar[]> {
    try {
      const { data, error } = await this.supabase
        .from('content_calendars')
        .select('*')
        .eq('brand_id', brandId)
        .eq('user_id', userId)
        .order('year', { ascending: false })
        .order('month', { ascending: false })

      if (error) {
        console.error('Error fetching brand calendars:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error in getBrandCalendars:', error)
      return []
    }
  }

  /**
   * Migration helper: Move localStorage calendar data to Supabase
   */
  async migrateFromLocalStorage(
    brandId: string,
    userId: string,
    localStorageData: ContentItem[]
  ): Promise<boolean> {
    try {
      if (!localStorageData || localStorageData.length === 0) {
        return true // Nothing to migrate
      }

      // Group content by month/year
      const contentByMonth = localStorageData.reduce((acc, item) => {
        const date = new Date(item.date)
        const month = date.getMonth() + 1 // JavaScript months are 0-indexed
        const year = date.getFullYear()
        const key = `${year}-${month}`

        if (!acc[key]) {
          acc[key] = { month, year, items: [] }
        }
        acc[key].items.push(item)

        return acc
      }, {} as Record<string, { month: number; year: number; items: ContentItem[] }>)

      // Save each month's content
      for (const { month, year, items } of Object.values(contentByMonth)) {
        await this.saveCalendarContent(brandId, userId, month, year, items)
      }

      return true
    } catch (error) {
      console.error('Error migrating from localStorage:', error)
      return false
    }
  }

  /**
   * Get content statistics for a brand
   */
  async getContentStats(brandId: string, userId: string): Promise<{
    totalCalendars: number
    totalPosts: number
    platformBreakdown: Record<string, number>
  }> {
    try {
      const calendars = await this.getBrandCalendars(brandId, userId)

      let totalPosts = 0
      const platformBreakdown: Record<string, number> = {}

      calendars.forEach(calendar => {
        const content = calendar.content as unknown as ContentItem[]
        if (Array.isArray(content)) {
          totalPosts += content.length

          content.forEach(item => {
            if (item && typeof item === 'object' && 'platform' in item) {
              platformBreakdown[item.platform] = (platformBreakdown[item.platform] || 0) + 1
            }
          })
        }
      })

      return {
        totalCalendars: calendars.length,
        totalPosts,
        platformBreakdown
      }
    } catch (error) {
      console.error('Error getting content stats:', error)
      return {
        totalCalendars: 0,
        totalPosts: 0,
        platformBreakdown: {}
      }
    }
  }

  /**
   * Get version history for calendar content
   */
  async getVersionHistory(
    brandId: string,
    userId: string,
    month: number,
    year: number
  ): Promise<ContentVersion[]> {
    const result = await safeAsyncOperation(
      async () => {
        const { data, error } = await this.supabase
          .rpc('get_content_version_history', {
            p_brand_id: brandId,
            p_user_id: userId,
            p_month: month,
            p_year: year
          })

        if (error) {
          throw createApiError(
            `Failed to load version history: ${error.message}`,
            500,
            error.code,
            error
          )
        }

        return data || []
      },
      {
        context: 'getVersionHistory',
        showErrorToast: false,
        fallbackValue: []
      }
    )

    return result || []
  }

  /**
   * Restore a specific version
   */
  async restoreVersion(
    versionId: string,
    userId: string,
    notes: string = 'Restored from previous version'
  ): Promise<string | null> {
    return await safeAsyncOperation(
      async () => {
        const { data, error } = await this.supabase
          .rpc('restore_content_version', {
            p_version_id: versionId,
            p_user_id: userId,
            p_notes: notes
          })

        if (error) {
          throw createApiError(
            `Failed to restore version: ${error.message}`,
            500,
            error.code,
            error
          )
        }

        return data
      },
      {
        context: 'restoreVersion',
        showErrorToast: false
      }
    )
  }

  /**
   * Get a specific version's content
   */
  async getVersionContent(
    versionId: string,
    userId: string
  ): Promise<ContentItem[]> {
    const result = await safeAsyncOperation(
      async () => {
        const { data, error } = await this.supabase
          .from('content_calendars')
          .select('content')
          .eq('id', versionId)
          .eq('user_id', userId)
          .single()

        if (error) {
          throw createApiError(
            `Failed to load version content: ${error.message}`,
            error.code === 'PGRST116' ? 404 : 500,
            error.code,
            error
          )
        }

        return (data.content as ContentItem[]) || []
      },
      {
        context: 'getVersionContent',
        showErrorToast: false,
        fallbackValue: []
      }
    )

    return result || []
  }

  /**
   * Clean up old versions (keep last 10)
   */
  async cleanupOldVersions(): Promise<number> {
    const result = await safeAsyncOperation(
      async () => {
        const { data, error } = await this.supabase
          .rpc('cleanup_old_versions')

        if (error) {
          throw createApiError(
            `Failed to cleanup old versions: ${error.message}`,
            500,
            error.code,
            error
          )
        }

        return data || 0
      },
      {
        context: 'cleanupOldVersions',
        showErrorToast: false,
        fallbackValue: 0
      }
    )

    return result || 0
  }
}

export const contentCalendarService = new ContentCalendarService()
