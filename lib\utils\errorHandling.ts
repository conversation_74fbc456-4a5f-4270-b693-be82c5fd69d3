import { toast } from 'react-hot-toast'

export interface RetryOptions {
  maxRetries?: number
  delay?: number
  backoff?: boolean
}

export interface ApiError extends Error {
  status?: number
  code?: string
  details?: any
}

/**
 * Creates a custom API error with additional context
 */
export function createApiError(
  message: string,
  status?: number,
  code?: string,
  details?: any
): ApiError {
  const error = new Error(message) as ApiError
  error.status = status
  error.code = code
  error.details = details
  return error
}

/**
 * Retry function with exponential backoff
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const { maxRetries = 3, delay = 1000, backoff = true } = options
  let lastError: Error

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      
      if (attempt === maxRetries) {
        throw lastError
      }

      // Calculate delay with exponential backoff
      const currentDelay = backoff ? delay * Math.pow(2, attempt) : delay
      await new Promise(resolve => setTimeout(resolve, currentDelay))
    }
  }

  throw lastError!
}

/**
 * Handle API errors with user-friendly messages
 */
export function handleApiError(error: unknown, context?: string): ApiError {
  console.error(`API Error${context ? ` in ${context}` : ''}:`, error)

  if (error instanceof Error) {
    const apiError = error as ApiError

    // Handle specific error types
    switch (apiError.status) {
      case 401:
        toast.error('Authentication required. Please sign in again.')
        // Redirect to login could be handled here
        break
      case 403:
        toast.error('You don\'t have permission to perform this action.')
        break
      case 404:
        toast.error('The requested resource was not found.')
        break
      case 429:
        toast.error('Too many requests. Please wait a moment and try again.')
        break
      case 500:
        toast.error('Server error. Please try again later.')
        break
      default:
        if (apiError.status && apiError.status >= 400) {
          toast.error(apiError.message || 'An error occurred. Please try again.')
        } else {
          toast.error('Network error. Please check your connection and try again.')
        }
    }

    return apiError
  }

  // Handle non-Error objects
  const genericError = createApiError(
    'An unexpected error occurred',
    undefined,
    'UNKNOWN_ERROR',
    error
  )
  toast.error('An unexpected error occurred. Please try again.')
  return genericError
}

/**
 * Wrapper for async operations with error handling and retry
 */
export async function safeAsyncOperation<T>(
  operation: () => Promise<T>,
  options: {
    context?: string
    retryOptions?: RetryOptions
    showErrorToast?: boolean
    fallbackValue?: T
  } = {}
): Promise<T | null> {
  const {
    context,
    retryOptions,
    showErrorToast = true,
    fallbackValue
  } = options

  try {
    if (retryOptions) {
      return await withRetry(operation, retryOptions)
    } else {
      return await operation()
    }
  } catch (error) {
    if (showErrorToast) {
      handleApiError(error, context)
    } else {
      console.error(`Error${context ? ` in ${context}` : ''}:`, error)
    }
    
    return fallbackValue ?? null
  }
}

/**
 * Validate required environment variables
 */
export function validateEnvironment() {
  const required = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ]

  const missing = required.filter(key => !process.env[key])
  
  if (missing.length > 0) {
    throw createApiError(
      `Missing required environment variables: ${missing.join(', ')}`,
      undefined,
      'ENV_MISSING'
    )
  }
}

/**
 * Check if error is a network error
 */
export function isNetworkError(error: unknown): boolean {
  if (error instanceof Error) {
    return (
      error.message.includes('fetch') ||
      error.message.includes('network') ||
      error.message.includes('NetworkError') ||
      error.message.includes('Failed to fetch')
    )
  }
  return false
}

/**
 * Check if error is retryable
 */
export function isRetryableError(error: unknown): boolean {
  if (error instanceof Error) {
    const apiError = error as ApiError
    
    // Retry on network errors
    if (isNetworkError(error)) {
      return true
    }
    
    // Retry on specific HTTP status codes
    if (apiError.status) {
      return [408, 429, 500, 502, 503, 504].includes(apiError.status)
    }
  }
  
  return false
}

/**
 * Format error message for display
 */
export function formatErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    const apiError = error as ApiError
    
    // Use custom message if available
    if (apiError.message && apiError.message !== 'Error') {
      return apiError.message
    }
    
    // Fallback based on status code
    switch (apiError.status) {
      case 400:
        return 'Invalid request. Please check your input and try again.'
      case 401:
        return 'Authentication required. Please sign in.'
      case 403:
        return 'Access denied. You don\'t have permission for this action.'
      case 404:
        return 'Resource not found.'
      case 429:
        return 'Too many requests. Please wait and try again.'
      case 500:
        return 'Server error. Please try again later.'
      default:
        return 'An unexpected error occurred. Please try again.'
    }
  }
  
  return 'An unexpected error occurred. Please try again.'
}
