'use client'

import { useState } from 'react'
import { X, Download, FileText, Calendar, Table, CheckCircle } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface ContentItem {
  id: string
  date: string
  platform: string
  type: string
  caption: string
  hashtags: string[]
  visualPrompt: string
  bestTime: string
}

interface BrandData {
  brandName: string
  industry: string
  tone: string
  targetAudience: string
  platforms: string[]
}

interface ExportModalProps {
  content: ContentItem[]
  brandData: BrandData
  onClose: () => void
}

type ExportFormat = 'csv' | 'json' | 'calendar'

export default function ExportModal({ content, brandData, onClose }: ExportModalProps) {
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>('csv')
  const [isExporting, setIsExporting] = useState(false)

  const exportFormats = [
    {
      id: 'csv' as ExportFormat,
      name: 'CSV File',
      description: 'Spreadsheet format for Excel, Google Sheets',
      icon: <Table className="w-6 h-6" />,
      extension: '.csv'
    },
    {
      id: 'json' as ExportFormat,
      name: 'JSON File',
      description: 'Structured data format for developers',
      icon: <FileText className="w-6 h-6" />,
      extension: '.json'
    },
    {
      id: 'calendar' as ExportFormat,
      name: 'Calendar File',
      description: 'Import into Google Calendar, Outlook',
      icon: <Calendar className="w-6 h-6" />,
      extension: '.ics'
    }
  ]

  const generateCSV = () => {
    const headers = ['Date', 'Platform', 'Type', 'Caption', 'Hashtags', 'Visual Prompt', 'Best Time']
    const rows = content.map(item => [
      item.date,
      item.platform,
      item.type,
      `"${item.caption.replace(/"/g, '""')}"`,
      `"${item.hashtags.join(' ')}"`,
      `"${item.visualPrompt.replace(/"/g, '""')}"`,
      item.bestTime
    ])
    
    return [headers, ...rows].map(row => row.join(',')).join('\n')
  }

  const generateJSON = () => {
    return JSON.stringify({
      brandData,
      content,
      exportDate: new Date().toISOString(),
      totalPosts: content.length
    }, null, 2)
  }

  const generateICS = () => {
    const icsHeader = [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//ViralPath.ai//Content Calendar//EN',
      'CALSCALE:GREGORIAN'
    ].join('\r\n')

    const events = content.map(item => {
      const date = new Date(item.date)
      const [hours, minutes] = item.bestTime.split(':')
      date.setHours(parseInt(hours), parseInt(minutes))
      
      const formatDate = (d: Date) => {
        return d.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z'
      }

      return [
        'BEGIN:VEVENT',
        `UID:${item.id}@viralpath.ai`,
        `DTSTART:${formatDate(date)}`,
        `DTEND:${formatDate(new Date(date.getTime() + 30 * 60000))}`, // 30 min duration
        `SUMMARY:${item.platform.toUpperCase()} Post - ${item.type}`,
        `DESCRIPTION:${item.caption.substring(0, 100)}...\n\nHashtags: ${item.hashtags.join(' ')}\n\nVisual: ${item.visualPrompt}`,
        `LOCATION:${item.platform}`,
        'END:VEVENT'
      ].join('\r\n')
    }).join('\r\n')

    const icsFooter = 'END:VCALENDAR'
    
    return [icsHeader, events, icsFooter].join('\r\n')
  }

  const handleExport = async () => {
    setIsExporting(true)
    
    try {
      let fileContent = ''
      let mimeType = ''
      let fileName = ''
      
      switch (selectedFormat) {
        case 'csv':
          fileContent = generateCSV()
          mimeType = 'text/csv'
          fileName = `${brandData.brandName.replace(/\s+/g, '_')}_content_calendar.csv`
          break
        case 'json':
          fileContent = generateJSON()
          mimeType = 'application/json'
          fileName = `${brandData.brandName.replace(/\s+/g, '_')}_content_calendar.json`
          break
        case 'calendar':
          fileContent = generateICS()
          mimeType = 'text/calendar'
          fileName = `${brandData.brandName.replace(/\s+/g, '_')}_content_calendar.ics`
          break
      }
      
      const blob = new Blob([fileContent], { type: mimeType })
      const url = URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      URL.revokeObjectURL(url)
      
      toast.success(`Calendar exported as ${selectedFormat.toUpperCase()} successfully!`)
      onClose()
    } catch (error) {
      toast.error('Failed to export calendar. Please try again.')
      console.error('Export error:', error)
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-heading font-bold text-neutral-dark">
              Export Calendar
            </h2>
            <p className="text-gray-600 mt-1">
              Choose your preferred export format
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-150"
          >
            <X className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Export Summary */}
          <div className="bg-gradient-to-r from-primary-purple/5 to-primary-blue/5 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-neutral-dark mb-2">Export Summary</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Brand:</span>
                <span className="ml-2 font-medium">{brandData.brandName}</span>
              </div>
              <div>
                <span className="text-gray-600">Total Posts:</span>
                <span className="ml-2 font-medium">{content.length}</span>
              </div>
              <div>
                <span className="text-gray-600">Platforms:</span>
                <span className="ml-2 font-medium">{brandData.platforms.length}</span>
              </div>
              <div>
                <span className="text-gray-600">Industry:</span>
                <span className="ml-2 font-medium">{brandData.industry}</span>
              </div>
            </div>
          </div>

          {/* Format Selection */}
          <div className="space-y-4 mb-6">
            <h3 className="font-semibold text-neutral-dark">Select Export Format</h3>
            
            <div className="grid gap-3">
              {exportFormats.map((format) => (
                <button
                  key={format.id}
                  onClick={() => setSelectedFormat(format.id)}
                  className={`p-4 border-2 rounded-lg text-left transition-all duration-150 ${
                    selectedFormat === format.id
                      ? 'border-primary-purple bg-primary-purple/5'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start gap-4">
                    <div className={`p-2 rounded-lg ${
                      selectedFormat === format.id
                        ? 'bg-primary-purple text-white'
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {format.icon}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-semibold text-neutral-dark">
                          {format.name}
                        </span>
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          {format.extension}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">
                        {format.description}
                      </p>
                    </div>
                    {selectedFormat === format.id && (
                      <CheckCircle className="w-5 h-5 text-primary-purple" />
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Export Instructions */}
          <div className="bg-blue-50 rounded-lg p-4 mb-6">
            <h4 className="font-medium text-blue-900 mb-2">📋 What's included:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• All {content.length} posts with captions and hashtags</li>
              <li>• Visual content prompts for each post</li>
              <li>• Optimal posting times for each platform</li>
              <li>• Platform-specific content organization</li>
              {selectedFormat === 'calendar' && (
                <li>• Calendar events you can import into any calendar app</li>
              )}
            </ul>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors duration-150"
          >
            Cancel
          </button>
          
          <button
            onClick={handleExport}
            disabled={isExporting || content.length === 0}
            className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-150 ${
              isExporting || content.length === 0
                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                : 'btn-primary'
            }`}
          >
            <Download className="w-4 h-4" />
            {isExporting ? 'Exporting...' : `Export ${selectedFormat.toUpperCase()}`}
          </button>
        </div>
      </div>
    </div>
  )
}