# Configure Supabase Auth Security Settings

## 🔐 Step-by-Step Configuration Guide

### 1. Enable Leaked Password Protection

1. **Access Supabase Dashboard**
   - Go to [supabase.com](https://supabase.com)
   - Sign in to your account
   - Select your ViralPath.ai project

2. **Navigate to Authentication Settings**
   - Click **"Authentication"** in the left sidebar
   - Click **"Settings"** tab
   - Scroll to **"Password Security"** section

3. **Enable Leaked Password Protection**
   - Find **"Leaked Password Protection"** toggle
   - Switch it to **ON/Enabled**
   - This will check passwords against HaveIBeenPwned.org database
   - Users with compromised passwords will be required to change them

### 2. Configure Multi-Factor Authentication (MFA)

1. **Access MFA Settings**
   - In Authentication → Settings
   - Scroll to **"Multi-Factor Authentication"** section

2. **Enable TOTP (Recommended)**
   - Find **"Time-based One-Time Password (TOTP)"**
   - Switch to **Enabled**
   - This allows users to use Google Authenticator, Authy, etc.

3. **Optional: Enable SMS MFA**
   - Find **"SMS"** option
   - Enable if you want SMS-based 2FA
   - Note: This may require additional setup and costs

4. **Optional: Enable Phone MFA**
   - Find **"Phone"** option
   - Enable for voice call verification
   - Note: This may require additional setup and costs

### 3. Update Application Code (Optional Enhancement)

Add MFA enrollment encouragement to your app:

```typescript
// In your dashboard or profile component
import { useState } from 'react'
import { createClient } from '@/lib/supabase/client'

export function MFASetup() {
  const [isEnrolling, setIsEnrolling] = useState(false)
  const supabase = createClient()

  const enrollMFA = async () => {
    setIsEnrolling(true)
    try {
      const { data, error } = await supabase.auth.mfa.enroll({
        factorType: 'totp',
        friendlyName: 'ViralPath.ai Account'
      })
      
      if (error) throw error
      
      // Show QR code to user for scanning with authenticator app
      console.log('MFA enrollment data:', data)
      
    } catch (error) {
      console.error('MFA enrollment error:', error)
    } finally {
      setIsEnrolling(false)
    }
  }

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-semibold mb-2">Secure Your Account</h3>
      <p className="text-gray-600 mb-4">
        Enable two-factor authentication for enhanced security
      </p>
      <button
        onClick={enrollMFA}
        disabled={isEnrolling}
        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
      >
        {isEnrolling ? 'Setting up...' : 'Enable 2FA'}
      </button>
    </div>
  )
}
```

### 4. Verification Steps

After configuring the settings:

1. **Test Leaked Password Protection**
   - Try registering with a known compromised password (e.g., "password123")
   - Should be rejected with appropriate error message

2. **Test MFA Enrollment**
   - Sign up for a new account
   - Try enrolling in MFA
   - Verify QR code generation works
   - Test authentication with MFA enabled

3. **Check Security Warnings**
   - Go to Supabase Dashboard
   - Check if security warnings are resolved
   - Run database linter again if available

### 5. User Communication

Consider adding these security features to your app:

1. **Security Dashboard**
   - Show MFA status
   - Allow MFA enrollment/management
   - Display security recommendations

2. **Onboarding Flow**
   - Encourage MFA setup during registration
   - Explain security benefits
   - Make it easy to enable

3. **Security Notifications**
   - Notify users about security improvements
   - Send reminders for MFA setup
   - Alert about suspicious activity

### 6. Monitoring and Maintenance

1. **Regular Checks**
   - Monthly security audit
   - Review authentication logs
   - Monitor failed login attempts

2. **User Support**
   - Help users with MFA setup
   - Provide recovery options
   - Document troubleshooting steps

3. **Updates**
   - Keep auth settings current
   - Review new Supabase security features
   - Update documentation as needed

---

## 📋 Configuration Checklist

- [ ] Leaked Password Protection enabled
- [ ] TOTP MFA enabled
- [ ] SMS MFA configured (optional)
- [ ] Phone MFA configured (optional)
- [ ] Test password validation
- [ ] Test MFA enrollment
- [ ] Update app UI for MFA
- [ ] Document user instructions
- [ ] Monitor security metrics

---

## 🚨 Important Notes

1. **Backup Codes**: Ensure users can generate backup codes for MFA
2. **Recovery Process**: Have a clear account recovery process
3. **User Education**: Educate users about security benefits
4. **Testing**: Thoroughly test all authentication flows
5. **Support**: Be ready to help users with setup issues

---

*Configuration guide created: January 8, 2025*
