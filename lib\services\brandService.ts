import { createClient } from '@/lib/supabase/client'
import type { Database } from '@/lib/supabase/database.types'

type Brand = Database['public']['Tables']['brands']['Row']
type BrandInsert = Database['public']['Tables']['brands']['Insert']
type BrandUpdate = Database['public']['Tables']['brands']['Update']

export interface BrandData {
  name: string
  industry: string
  tone: string
  target_audience: string
  platforms: string[]
  description?: string
  website?: string
  logo_url?: string
}

class BrandService {
  private supabase = createClient()

  async createBrand(userId: string, brandData: BrandData): Promise<Brand | null> {
    try {
      // First, set all existing brands for this user to inactive
      await this.supabase
        .from('brands')
        .update({ is_active: false })
        .eq('user_id', userId)

      // Create new brand as active
      const { data, error } = await this.supabase
        .from('brands')
        .insert({
          user_id: userId,
          ...brandData,
          is_active: true
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating brand:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error in createBrand:', error)
      return null
    }
  }

  async getUserBrands(userId: string): Promise<Brand[]> {
    try {
      const { data, error } = await this.supabase
        .from('brands')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching brands:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error in getUserBrands:', error)
      return []
    }
  }

  async getActiveBrand(userId: string): Promise<Brand | null> {
    try {
      const { data, error } = await this.supabase
        .from('brands')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .single()

      if (error) {
        console.error('Error fetching active brand:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error in getActiveBrand:', error)
      return null
    }
  }

  async setActiveBrand(userId: string, brandId: string): Promise<boolean> {
    try {
      // Set all brands to inactive
      await this.supabase
        .from('brands')
        .update({ is_active: false })
        .eq('user_id', userId)

      // Set selected brand to active
      const { error } = await this.supabase
        .from('brands')
        .update({ is_active: true })
        .eq('id', brandId)
        .eq('user_id', userId)

      if (error) {
        console.error('Error setting active brand:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error in setActiveBrand:', error)
      return false
    }
  }

  async updateBrand(brandId: string, userId: string, updates: Partial<BrandData>): Promise<Brand | null> {
    try {
      const { data, error } = await this.supabase
        .from('brands')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', brandId)
        .eq('user_id', userId)
        .select()
        .single()

      if (error) {
        console.error('Error updating brand:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error in updateBrand:', error)
      return null
    }
  }

  async deleteBrand(brandId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('brands')
        .delete()
        .eq('id', brandId)
        .eq('user_id', userId)

      if (error) {
        console.error('Error deleting brand:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error in deleteBrand:', error)
      return false
    }
  }

  async duplicateBrand(brandId: string, userId: string, newName: string): Promise<Brand | null> {
    try {
      // Get the original brand
      const { data: originalBrand, error: fetchError } = await this.supabase
        .from('brands')
        .select('*')
        .eq('id', brandId)
        .eq('user_id', userId)
        .single()

      if (fetchError || !originalBrand) {
        console.error('Error fetching original brand:', fetchError)
        return null
      }

      // Create duplicate with new name
      const duplicateData: BrandInsert = {
        user_id: userId,
        name: newName,
        industry: originalBrand.industry,
        tone: originalBrand.tone,
        target_audience: originalBrand.target_audience,
        platforms: originalBrand.platforms,
        description: originalBrand.description,
        website: originalBrand.website,
        logo_url: originalBrand.logo_url,
        is_active: false
      }

      const { data, error } = await this.supabase
        .from('brands')
        .insert(duplicateData)
        .select()
        .single()

      if (error) {
        console.error('Error duplicating brand:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error in duplicateBrand:', error)
      return null
    }
  }

  // Migration helper: Move localStorage data to Supabase
  async migrateBrandFromLocalStorage(userId: string, localBrandData: any): Promise<Brand | null> {
    try {
      const brandData: BrandData = {
        name: localBrandData.brandName || 'My Brand',
        industry: localBrandData.industry || '',
        tone: localBrandData.tone || '',
        target_audience: localBrandData.targetAudience || '',
        platforms: localBrandData.platforms || []
      }

      return await this.createBrand(userId, brandData)
    } catch (error) {
      console.error('Error migrating brand from localStorage:', error)
      return null
    }
  }
}

export const brandService = new BrandService()