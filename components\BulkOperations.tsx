'use client'

import { useState, useEffect } from 'react'
import {
  CheckSquare,
  Square,
  Edit3,
  RefreshCw,
  Download,
  Trash2,
  Copy,
  Calendar,
  X,
  Play,
  Pause,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react'
import { BulkOperationsService, BulkEditOptions, BulkExportOptions } from '@/lib/services/bulkOperationsService'
import { ContentItem } from '@/lib/types/content'
import { toast } from 'react-hot-toast'

interface BulkOperationsProps {
  items: ContentItem[]
  selectedItems: string[]
  onSelectionChange: (selectedIds: string[]) => void
  onItemsUpdate: (updatedItems: ContentItem[]) => void
  onClose: () => void
  brandData: any
}

export default function BulkOperations({
  items,
  selectedItems,
  onSelectionChange,
  onItemsUpdate,
  onClose,
  brandData
}: BulkOperationsProps) {
  const [activeOperation, setActiveOperation] = useState<string | null>(null)
  const [operationProgress, setOperationProgress] = useState(0)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showExportModal, setShowExportModal] = useState(false)
  const [editOptions, setEditOptions] = useState<BulkEditOptions>({})
  const [exportOptions, setExportOptions] = useState<BulkExportOptions>({
    format: 'csv',
    includeFields: ['date', 'platform', 'content', 'hashtags', 'status']
  })

  const selectedItemsData = items.filter(item => selectedItems.includes(item.id))
  const hasSelection = selectedItems.length > 0

  const handleSelectAll = () => {
    if (selectedItems.length === items.length) {
      onSelectionChange([])
    } else {
      onSelectionChange(items.map(item => item.id))
    }
  }

  const handleBulkEdit = async () => {
    if (!hasSelection) return

    setActiveOperation('edit')
    setOperationProgress(0)

    try {
      const updatedItems = await BulkOperationsService.executeBulkEdit(
        selectedItemsData,
        editOptions,
        setOperationProgress
      )

      // Update the items in the parent component
      const newItems = items.map(item => {
        const updated = updatedItems.find(u => u.id === item.id)
        return updated || item
      })

      onItemsUpdate(newItems)
      toast.success(`Successfully updated ${selectedItems.length} items`)
      setShowEditModal(false)
    } catch (error) {
      toast.error('Failed to update items')
    } finally {
      setActiveOperation(null)
      setOperationProgress(0)
    }
  }

  const handleBulkRegenerate = async () => {
    if (!hasSelection) return

    setActiveOperation('regenerate')
    setOperationProgress(0)

    try {
      const regeneratedItems = await BulkOperationsService.executeBulkRegenerate(
        selectedItemsData,
        brandData,
        setOperationProgress
      )

      // Update the items in the parent component
      const newItems = items.map(item => {
        const regenerated = regeneratedItems.find(r => r.id === item.id)
        return regenerated || item
      })

      onItemsUpdate(newItems)
      toast.success(`Successfully regenerated ${selectedItems.length} items`)
    } catch (error) {
      toast.error('Failed to regenerate items')
    } finally {
      setActiveOperation(null)
      setOperationProgress(0)
    }
  }

  const handleBulkExport = async () => {
    if (!hasSelection) return

    setActiveOperation('export')
    setOperationProgress(0)

    try {
      const exportData = await BulkOperationsService.executeBulkExport(
        selectedItemsData,
        exportOptions,
        setOperationProgress
      )

      // Create and download file
      const blob = new Blob([exportData], {
        type: exportOptions.format === 'csv' ? 'text/csv' : 'application/json'
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `content-export-${new Date().toISOString().split('T')[0]}.${exportOptions.format}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast.success(`Successfully exported ${selectedItems.length} items`)
      setShowExportModal(false)
    } catch (error) {
      toast.error('Failed to export items')
    } finally {
      setActiveOperation(null)
      setOperationProgress(0)
    }
  }

  const handleBulkDuplicate = async () => {
    if (!hasSelection) return

    setActiveOperation('duplicate')
    setOperationProgress(0)

    try {
      const duplicatedItems = await BulkOperationsService.executeBulkDuplicate(
        selectedItemsData,
        undefined,
        setOperationProgress
      )

      // Add duplicated items to the parent component
      onItemsUpdate([...items, ...duplicatedItems])
      toast.success(`Successfully duplicated ${selectedItems.length} items`)
    } catch (error) {
      toast.error('Failed to duplicate items')
    } finally {
      setActiveOperation(null)
      setOperationProgress(0)
    }
  }

  const handleBulkDelete = async () => {
    if (!hasSelection) return

    if (!confirm(`Are you sure you want to delete ${selectedItems.length} items? This action cannot be undone.`)) {
      return
    }

    setActiveOperation('delete')
    setOperationProgress(0)

    try {
      await BulkOperationsService.executeBulkDelete(
        selectedItems,
        setOperationProgress
      )

      // Remove deleted items from the parent component
      const remainingItems = items.filter(item => !selectedItems.includes(item.id))
      onItemsUpdate(remainingItems)
      onSelectionChange([])
      toast.success(`Successfully deleted ${selectedItems.length} items`)
    } catch (error) {
      toast.error('Failed to delete items')
    } finally {
      setActiveOperation(null)
      setOperationProgress(0)
    }
  }

  const getOperationIcon = (operation: string) => {
    switch (operation) {
      case 'edit': return <Edit3 className="w-4 h-4" />
      case 'regenerate': return <RefreshCw className="w-4 h-4" />
      case 'export': return <Download className="w-4 h-4" />
      case 'duplicate': return <Copy className="w-4 h-4" />
      case 'delete': return <Trash2 className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-neutral-dark flex items-center gap-2">
              <CheckSquare className="w-5 h-5 text-primary-purple" />
              Bulk Operations
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              {selectedItems.length} of {items.length} items selected
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-150"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Selection Controls */}
          <div className="flex items-center justify-between mb-6 p-4 bg-gray-50 rounded-xl">
            <div className="flex items-center gap-4">
              <button
                onClick={handleSelectAll}
                className="flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-primary-purple transition-colors"
              >
                {selectedItems.length === items.length ? (
                  <CheckSquare className="w-4 h-4" />
                ) : (
                  <Square className="w-4 h-4" />
                )}
                {selectedItems.length === items.length ? 'Deselect All' : 'Select All'}
              </button>
              <span className="text-sm text-gray-500">
                {selectedItems.length} selected
              </span>
            </div>

            {hasSelection && (
              <div className="text-sm text-gray-600">
                Ready for bulk operations
              </div>
            )}
          </div>

          {/* Operation Progress */}
          {activeOperation && (
            <div className="mb-6 p-4 bg-blue-50 rounded-xl">
              <div className="flex items-center gap-3 mb-3">
                {getOperationIcon(activeOperation)}
                <span className="font-medium text-blue-900 capitalize">
                  {activeOperation === 'regenerate' ? 'Regenerating' : `${activeOperation}ing`} {selectedItems.length} items...
                </span>
              </div>
              <div className="w-full bg-blue-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${operationProgress}%` }}
                />
              </div>
              <div className="text-sm text-blue-700 mt-2">
                {operationProgress}% complete
              </div>
            </div>
          )}

          {/* Bulk Operations */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {/* Bulk Edit */}
            <button
              onClick={() => setShowEditModal(true)}
              disabled={!hasSelection || !!activeOperation}
              className="p-4 border border-gray-200 rounded-xl hover:border-primary-purple hover:bg-primary-purple/5 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Edit3 className="w-6 h-6 text-primary-purple mx-auto mb-2" />
              <div className="text-sm font-medium text-gray-900">Bulk Edit</div>
              <div className="text-xs text-gray-600 mt-1">Edit properties</div>
            </button>

            {/* Bulk Regenerate */}
            <button
              onClick={handleBulkRegenerate}
              disabled={!hasSelection || !!activeOperation}
              className="p-4 border border-gray-200 rounded-xl hover:border-green-500 hover:bg-green-50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <RefreshCw className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <div className="text-sm font-medium text-gray-900">Regenerate</div>
              <div className="text-xs text-gray-600 mt-1">AI refresh content</div>
            </button>

            {/* Bulk Export */}
            <button
              onClick={() => setShowExportModal(true)}
              disabled={!hasSelection || !!activeOperation}
              className="p-4 border border-gray-200 rounded-xl hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Download className="w-6 h-6 text-blue-600 mx-auto mb-2" />
              <div className="text-sm font-medium text-gray-900">Export</div>
              <div className="text-xs text-gray-600 mt-1">Download data</div>
            </button>

            {/* Bulk Duplicate */}
            <button
              onClick={handleBulkDuplicate}
              disabled={!hasSelection || !!activeOperation}
              className="p-4 border border-gray-200 rounded-xl hover:border-purple-500 hover:bg-purple-50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Copy className="w-6 h-6 text-purple-600 mx-auto mb-2" />
              <div className="text-sm font-medium text-gray-900">Duplicate</div>
              <div className="text-xs text-gray-600 mt-1">Create copies</div>
            </button>

            {/* Bulk Schedule */}
            <button
              disabled={!hasSelection || !!activeOperation}
              className="p-4 border border-gray-200 rounded-xl hover:border-orange-500 hover:bg-orange-50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Calendar className="w-6 h-6 text-orange-600 mx-auto mb-2" />
              <div className="text-sm font-medium text-gray-900">Schedule</div>
              <div className="text-xs text-gray-600 mt-1">Set posting times</div>
            </button>

            {/* Bulk Delete */}
            <button
              onClick={handleBulkDelete}
              disabled={!hasSelection || !!activeOperation}
              className="p-4 border border-gray-200 rounded-xl hover:border-red-500 hover:bg-red-50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Trash2 className="w-6 h-6 text-red-600 mx-auto mb-2" />
              <div className="text-sm font-medium text-gray-900">Delete</div>
              <div className="text-xs text-gray-600 mt-1">Remove items</div>
            </button>
          </div>

          {/* Selection Summary */}
          {hasSelection && (
            <div className="mt-6 p-4 bg-gray-50 rounded-xl">
              <h3 className="font-medium text-gray-900 mb-3">Selection Summary</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="text-gray-600">Total Items</div>
                  <div className="font-medium">{selectedItems.length}</div>
                </div>
                <div>
                  <div className="text-gray-600">Platforms</div>
                  <div className="font-medium">
                    {new Set(selectedItemsData.map(item => item.platform)).size}
                  </div>
                </div>
                <div>
                  <div className="text-gray-600">Draft Items</div>
                  <div className="font-medium">
                    {selectedItemsData.filter(item => item.status === 'draft').length}
                  </div>
                </div>
                <div>
                  <div className="text-gray-600">Published Items</div>
                  <div className="font-medium">
                    {selectedItemsData.filter(item => item.status === 'published').length}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Edit Modal */}
        {showEditModal && (
          <div className="fixed inset-0 z-60 flex items-center justify-center bg-black/50 backdrop-blur-sm">
            <div className="bg-white rounded-xl shadow-2xl w-full max-w-md mx-4">
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h3 className="font-semibold text-neutral-dark">Bulk Edit Options</h3>
                <button
                  onClick={() => setShowEditModal(false)}
                  className="p-1 hover:bg-gray-100 rounded transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              <div className="p-4 space-y-4">
                {/* Platform */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Change Platform
                  </label>
                  <select
                    value={editOptions.platform || ''}
                    onChange={(e) => setEditOptions(prev => ({ ...prev, platform: e.target.value || undefined }))}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-purple focus:border-transparent"
                  >
                    <option value="">Keep current</option>
                    <option value="instagram">Instagram</option>
                    <option value="tiktok">TikTok</option>
                    <option value="linkedin">LinkedIn</option>
                    <option value="twitter">Twitter</option>
                  </select>
                </div>

                {/* Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Change Status
                  </label>
                  <select
                    value={editOptions.status || ''}
                    onChange={(e) => setEditOptions(prev => ({ ...prev, status: e.target.value as any || undefined }))}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-purple focus:border-transparent"
                  >
                    <option value="">Keep current</option>
                    <option value="draft">Draft</option>
                    <option value="scheduled">Scheduled</option>
                    <option value="published">Published</option>
                  </select>
                </div>

                {/* Actions */}
                <div className="flex gap-3 pt-4">
                  <button
                    onClick={handleBulkEdit}
                    className="flex-1 bg-gradient-primary text-white py-2 rounded-lg hover:shadow-lg transition-all duration-200 text-sm font-medium"
                  >
                    Apply Changes
                  </button>
                  <button
                    onClick={() => setShowEditModal(false)}
                    className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Export Modal */}
        {showExportModal && (
          <div className="fixed inset-0 z-60 flex items-center justify-center bg-black/50 backdrop-blur-sm">
            <div className="bg-white rounded-xl shadow-2xl w-full max-w-md mx-4">
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h3 className="font-semibold text-neutral-dark">Export Options</h3>
                <button
                  onClick={() => setShowExportModal(false)}
                  className="p-1 hover:bg-gray-100 rounded transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              <div className="p-4 space-y-4">
                {/* Format */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Export Format
                  </label>
                  <select
                    value={exportOptions.format}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, format: e.target.value as any }))}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-purple focus:border-transparent"
                  >
                    <option value="csv">CSV</option>
                    <option value="json">JSON</option>
                  </select>
                </div>

                {/* Fields */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Include Fields
                  </label>
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {['date', 'platform', 'content', 'hashtags', 'status', 'likes', 'comments', 'shares'].map(field => (
                      <label key={field} className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={exportOptions.includeFields.includes(field)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setExportOptions(prev => ({
                                ...prev,
                                includeFields: [...prev.includeFields, field]
                              }))
                            } else {
                              setExportOptions(prev => ({
                                ...prev,
                                includeFields: prev.includeFields.filter(f => f !== field)
                              }))
                            }
                          }}
                          className="rounded border-gray-300 text-primary-purple focus:ring-primary-purple"
                        />
                        <span className="text-sm capitalize">{field}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-3 pt-4">
                  <button
                    onClick={handleBulkExport}
                    className="flex-1 bg-gradient-primary text-white py-2 rounded-lg hover:shadow-lg transition-all duration-200 text-sm font-medium"
                  >
                    Export Data
                  </button>
                  <button
                    onClick={() => setShowExportModal(false)}
                    className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
