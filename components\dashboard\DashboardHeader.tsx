'use client'

import { useState } from 'react'
import {
  Sparkles,
  Building2,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Bell,
  Settings,
  LogOut,
  Download,
  History,
  Zap,
  RefreshCw,
  Cpu
} from 'lucide-react'
import ModelSelector from '@/components/ModelSelector'
import { ModelSettingsService } from '@/lib/services/modelSettingsService'

interface Brand {
  id: string
  name: string
  industry: string
}

interface DashboardHeaderProps {
  currentBrand: Brand | null
  brands: Brand[]
  currentMonth: Date | null
  user: { email?: string } | null
  calendarContent: any[]
  isGenerating: boolean
  onBrandManagerOpen: () => void
  onMonthChange: (date: Date) => void
  onExportOpen: () => void
  onVersionHistoryOpen: () => void
  onGenerateCalendar: () => void
  onSignOut: () => void
}

export default function DashboardHeader({
  currentBrand,
  brands,
  currentMonth,
  user,
  calendarContent,
  isGenerating,
  onBrandManagerOpen,
  onMonthChange,
  onExportOpen,
  onVersionHistoryOpen,
  onGenerateCalendar,
  onSignOut
}: DashboardHeaderProps) {
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showModelSelector, setShowModelSelector] = useState(false)
  const [currentModel, setCurrentModel] = useState(() => ModelSettingsService.getCurrentModel())

  const handlePreviousMonth = () => {
    if (!currentMonth) return
    const newDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1)
    onMonthChange(newDate)
  }

  const handleNextMonth = () => {
    if (!currentMonth) return
    const newDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1)
    onMonthChange(newDate)
  }

  const handleModelChange = (modelId: string) => {
    ModelSettingsService.updateSelectedModel(modelId)
    setCurrentModel(modelId)
  }

  const getModelDisplayName = (modelId: string) => {
    const modelInfo = ModelSettingsService.getModelInfo(modelId)
    return modelInfo.name
  }

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Left Section */}
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-heading font-bold text-neutral-dark">
                  {currentBrand?.name || 'ViralPath.ai'}
                </h1>
                {currentBrand && (
                  <p className="text-xs text-gray-500 capitalize">
                    {currentBrand.industry}
                  </p>
                )}
              </div>
            </div>

            {/* Brand Selector */}
            {brands.length > 0 && (
              <button
                onClick={onBrandManagerOpen}
                className="flex items-center gap-3 px-4 py-2 bg-gray-50 hover:bg-gray-100 rounded-xl transition-all duration-200 border border-gray-200 hover:border-gray-300"
              >
                <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center text-white text-sm font-bold">
                  {currentBrand?.name?.charAt(0) || 'B'}
                </div>
                <div className="text-left">
                  <div className="font-semibold text-gray-900 text-sm">
                    Switch Brand
                  </div>
                  <div className="text-xs text-gray-500">
                    {brands.length} brands
                  </div>
                </div>
                <ChevronDown className="w-4 h-4 text-gray-400" />
              </button>
            )}
          </div>

          {/* Center Section - Month Navigation */}
          <div className="flex items-center gap-1 bg-gray-50 rounded-xl p-1">
            <button
              onClick={handlePreviousMonth}
              className="p-2 hover:bg-white rounded-lg transition-all duration-200 hover:shadow-sm"
            >
              <ChevronLeft className="w-4 h-4 text-gray-600" />
            </button>
            <div className="px-4 py-2 min-w-[140px] text-center">
              <div className="font-semibold text-gray-900 text-sm">
                {currentMonth ? currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' }) : 'Loading...'}
              </div>
            </div>
            <button
              onClick={handleNextMonth}
              className="p-2 hover:bg-white rounded-lg transition-all duration-200 hover:shadow-sm"
            >
              <ChevronRight className="w-4 h-4 text-gray-600" />
            </button>
          </div>

          {/* Right Section */}
          <div className="flex items-center gap-3">
            {/* Notifications */}
            <button className="relative p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">
              <Bell className="w-5 h-5 text-gray-600" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
            </button>

            {/* Action Buttons */}
            <button
              onClick={onExportOpen}
              className="btn-secondary flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              Export
            </button>

            {calendarContent.length > 0 && (
              <button
                onClick={onVersionHistoryOpen}
                className="btn-secondary flex items-center gap-2"
              >
                <History className="w-4 h-4" />
                History
              </button>
            )}

            <div className="flex items-center gap-3">
              <div className="text-xs text-gray-500 hidden sm:block">
                <div className="flex items-center gap-1">
                  <Cpu className="w-3 h-3" />
                  <span>{getModelDisplayName(currentModel)}</span>
                  {ModelSettingsService.isFreeTierModel(currentModel) && (
                    <span className="px-1.5 py-0.5 bg-green-100 text-green-700 rounded text-xs font-medium">
                      Free
                    </span>
                  )}
                </div>
              </div>

              <button
                onClick={onGenerateCalendar}
                disabled={isGenerating}
                className="btn-primary flex items-center gap-2"
              >
                {isGenerating ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <Zap className="w-4 h-4" />
                )}
                {isGenerating ? 'Generating...' : 'Generate'}
              </button>
            </div>

            {/* User Menu */}
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center gap-2 p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              >
                <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center text-white text-sm font-bold">
                  {user?.email?.charAt(0).toUpperCase() || 'U'}
                </div>
                <ChevronDown className="w-4 h-4 text-gray-600" />
              </button>

              {showUserMenu && (
                <div className="absolute right-0 top-full mt-2 w-56 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50">
                  <div className="px-4 py-3 border-b border-gray-100">
                    <div className="font-medium text-gray-900">{user?.email}</div>
                    <div className="text-sm text-gray-500">Free Plan</div>
                  </div>
                  <button
                    onClick={() => {
                      setShowModelSelector(true)
                      setShowUserMenu(false)
                    }}
                    className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-50 flex items-center gap-3 transition-colors"
                  >
                    <Cpu className="w-4 h-4" />
                    <div className="flex-1">
                      <div className="text-sm font-medium">AI Model</div>
                      <div className="text-xs text-gray-500">{getModelDisplayName(currentModel)}</div>
                    </div>
                  </button>
                  <button
                    onClick={() => setShowUserMenu(false)}
                    className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-50 flex items-center gap-3 transition-colors"
                  >
                    <Settings className="w-4 h-4" />
                    Settings
                  </button>
                  <button
                    onClick={onSignOut}
                    className="w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 flex items-center gap-3 transition-colors"
                  >
                    <LogOut className="w-4 h-4" />
                    Sign Out
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Model Selector Modal */}
      <ModelSelector
        isOpen={showModelSelector}
        onClose={() => setShowModelSelector(false)}
        currentModel={currentModel}
        onModelChange={handleModelChange}
      />
    </header>
  )
}
