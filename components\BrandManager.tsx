'use client'

import { useState, useEffect } from 'react'
import { Plus, Edit3, Trash2, Copy, Building2, X, Save, ChevronDown, Check } from 'lucide-react'
import { brandService, type BrandData } from '@/lib/services/brandService'
import { toast } from 'react-hot-toast'
import type { Database } from '@/lib/supabase/database.types'
import { BrandCardSkeleton } from './SkeletonLoader'

type Brand = Database['public']['Tables']['brands']['Row']

interface BrandManagerProps {
  userId: string
  currentBrand: Brand | null
  onBrandChange: (brand: Brand | null) => void
  onClose: () => void
}

interface BrandFormData {
  name: string
  industry: string
  tone: string
  target_audience: string
  platforms: string[]
  description: string
  website: string
}

const AVAILABLE_PLATFORMS = [
  'Instagram',
  'TikTok',
  'LinkedIn',
  'X/Twitter',
  'Facebook',
  'YouTube',
  'Pinterest'
]

const TONE_OPTIONS = [
  'Professional',
  'Casual',
  'Friendly',
  'Authoritative',
  'Playful',
  'Inspirational',
  'Educational',
  'Humorous'
]

export default function BrandManager({ userId, currentBrand, onBrandChange, onClose }: BrandManagerProps) {
  const [brands, setBrands] = useState<Brand[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingBrand, setEditingBrand] = useState<Brand | null>(null)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState<BrandFormData>({
    name: '',
    industry: '',
    tone: '',
    target_audience: '',
    platforms: [],
    description: '',
    website: ''
  })

  useEffect(() => {
    loadBrands()
  }, [userId])

  const loadBrands = async () => {
    try {
      setLoading(true)
      const userBrands = await brandService.getUserBrands(userId)
      setBrands(userBrands)
    } catch (error) {
      console.error('Error loading brands:', error)
      toast.error('Failed to load brands')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateBrand = () => {
    setEditingBrand(null)
    setFormData({
      name: '',
      industry: '',
      tone: '',
      target_audience: '',
      platforms: [],
      description: '',
      website: ''
    })
    setShowForm(true)
  }

  const handleEditBrand = (brand: Brand) => {
    setEditingBrand(brand)
    setFormData({
      name: brand.name,
      industry: brand.industry,
      tone: brand.tone,
      target_audience: brand.target_audience,
      platforms: brand.platforms,
      description: brand.description || '',
      website: brand.website || ''
    })
    setShowForm(true)
  }

  const handleSaveBrand = async () => {
    if (!formData.name.trim()) {
      toast.error('Brand name is required')
      return
    }

    try {
      setSaving(true)
      let savedBrand: Brand | null = null

      if (editingBrand) {
        // Update existing brand
        savedBrand = await brandService.updateBrand(editingBrand.id, userId, formData)
      } else {
        // Create new brand
        savedBrand = await brandService.createBrand(userId, formData)
      }

      if (savedBrand) {
        await loadBrands()
        if (!editingBrand) {
          // If creating new brand, set it as active
          onBrandChange(savedBrand)
        } else if (currentBrand?.id === savedBrand.id) {
          // If editing current brand, update the current brand data
          onBrandChange(savedBrand)
        }
        setShowForm(false)
        toast.success(editingBrand ? 'Brand updated successfully!' : 'Brand created successfully!')
      } else {
        toast.error('Failed to save brand')
      }
    } catch (error) {
      console.error('Error saving brand:', error)
      toast.error('Failed to save brand')
    } finally {
      setSaving(false)
    }
  }

  const handleSwitchBrand = async (brand: Brand) => {
    try {
      const success = await brandService.setActiveBrand(userId, brand.id)
      if (success) {
        onBrandChange(brand)
        await loadBrands() // Refresh to update active status
        toast.success(`Switched to ${brand.name}`)
      } else {
        toast.error('Failed to switch brand')
      }
    } catch (error) {
      console.error('Error switching brand:', error)
      toast.error('Failed to switch brand')
    }
  }

  const handleDuplicateBrand = async (brand: Brand) => {
    try {
      const newName = `${brand.name} (Copy)`
      const duplicatedBrand = await brandService.duplicateBrand(brand.id, userId, newName)
      if (duplicatedBrand) {
        await loadBrands()
        toast.success('Brand duplicated successfully!')
      } else {
        toast.error('Failed to duplicate brand')
      }
    } catch (error) {
      console.error('Error duplicating brand:', error)
      toast.error('Failed to duplicate brand')
    }
  }

  const handleDeleteBrand = async (brand: Brand) => {
    if (!confirm(`Are you sure you want to delete "${brand.name}"? This action cannot be undone.`)) {
      return
    }

    try {
      const success = await brandService.deleteBrand(brand.id, userId)
      if (success) {
        await loadBrands()
        if (currentBrand?.id === brand.id) {
          // If deleting current brand, switch to another or null
          const remainingBrands = brands.filter(b => b.id !== brand.id)
          if (remainingBrands.length > 0) {
            await handleSwitchBrand(remainingBrands[0])
          } else {
            onBrandChange(null)
          }
        }
        toast.success('Brand deleted successfully!')
      } else {
        toast.error('Failed to delete brand')
      }
    } catch (error) {
      console.error('Error deleting brand:', error)
      toast.error('Failed to delete brand')
    }
  }

  const handlePlatformToggle = (platform: string) => {
    setFormData(prev => ({
      ...prev,
      platforms: prev.platforms.includes(platform)
        ? prev.platforms.filter(p => p !== platform)
        : [...prev.platforms, platform]
    }))
  }

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-neutral-dark flex items-center gap-2">
              <Building2 className="w-5 h-5 text-primary-purple" />
              Brand Manager
            </h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-150"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          <div className="flex h-[calc(90vh-80px)]">
            {/* Brand List Skeleton */}
            <div className="w-1/2 border-r border-gray-200 overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold text-neutral-dark">Your Brands</h3>
                  <div className="h-8 w-24 bg-gray-200 rounded animate-pulse"></div>
                </div>
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <BrandCardSkeleton key={index} />
                  ))}
                </div>
              </div>
            </div>

            {/* Form Skeleton */}
            <div className="w-1/2 overflow-y-auto">
              <div className="p-6">
                <div className="h-6 w-32 bg-gray-200 rounded animate-pulse mb-6"></div>
                <div className="space-y-4">
                  {Array.from({ length: 6 }).map((_, index) => (
                    <div key={index}>
                      <div className="h-4 w-20 bg-gray-200 rounded animate-pulse mb-2"></div>
                      <div className="h-10 w-full bg-gray-200 rounded animate-pulse"></div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-neutral-dark flex items-center gap-2">
            <Building2 className="w-5 h-5 text-primary-purple" />
            Brand Manager
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-150"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-80px)]">
          {/* Brand List */}
          <div className="w-1/2 border-r border-gray-200 overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-neutral-dark">Your Brands</h3>
                <button
                  onClick={handleCreateBrand}
                  className="btn-primary flex items-center gap-2 text-sm"
                >
                  <Plus className="w-4 h-4" />
                  New Brand
                </button>
              </div>

              <div className="space-y-3">
                {brands.map((brand) => (
                  <div
                    key={brand.id}
                    className={`p-4 rounded-lg border-2 transition-all duration-150 ${brand.is_active
                      ? 'border-primary-purple bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                      }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-neutral-dark">{brand.name}</h4>
                          {brand.is_active && (
                            <span className="bg-primary-purple text-white text-xs px-2 py-1 rounded-full">
                              Active
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{brand.industry}</p>
                        <div className="flex flex-wrap gap-1">
                          {brand.platforms.slice(0, 3).map((platform) => (
                            <span
                              key={platform}
                              className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
                            >
                              {platform}
                            </span>
                          ))}
                          {brand.platforms.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{brand.platforms.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-1 ml-2">
                        {!brand.is_active && (
                          <button
                            onClick={() => handleSwitchBrand(brand)}
                            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-150"
                            title="Switch to this brand"
                          >
                            <Check className="w-4 h-4 text-green-600" />
                          </button>
                        )}
                        <button
                          onClick={() => handleEditBrand(brand)}
                          className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-150"
                          title="Edit brand"
                        >
                          <Edit3 className="w-4 h-4 text-gray-600" />
                        </button>
                        <button
                          onClick={() => handleDuplicateBrand(brand)}
                          className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-150"
                          title="Duplicate brand"
                        >
                          <Copy className="w-4 h-4 text-gray-600" />
                        </button>
                        <button
                          onClick={() => handleDeleteBrand(brand)}
                          className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-150"
                          title="Delete brand"
                        >
                          <Trash2 className="w-4 h-4 text-red-600" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}

                {brands.length === 0 && (
                  <div className="text-center py-8">
                    <Building2 className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                    <p className="text-gray-500 mb-4">No brands created yet</p>
                    <button
                      onClick={handleCreateBrand}
                      className="btn-primary flex items-center gap-2 mx-auto"
                    >
                      <Plus className="w-4 h-4" />
                      Create Your First Brand
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Brand Form */}
          <div className="w-1/2 overflow-y-auto">
            {showForm ? (
              <div className="p-6">
                <h3 className="font-semibold text-neutral-dark mb-4">
                  {editingBrand ? 'Edit Brand' : 'Create New Brand'}
                </h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Brand Name *
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-purple focus:border-transparent"
                      placeholder="Enter brand name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Industry
                    </label>
                    <input
                      type="text"
                      value={formData.industry}
                      onChange={(e) => setFormData(prev => ({ ...prev, industry: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-purple focus:border-transparent"
                      placeholder="e.g., Technology, Fashion, Food"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Brand Tone
                    </label>
                    <select
                      value={formData.tone}
                      onChange={(e) => setFormData(prev => ({ ...prev, tone: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-purple focus:border-transparent"
                    >
                      <option value="">Select tone</option>
                      {TONE_OPTIONS.map((tone) => (
                        <option key={tone} value={tone}>{tone}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Target Audience
                    </label>
                    <input
                      type="text"
                      value={formData.target_audience}
                      onChange={(e) => setFormData(prev => ({ ...prev, target_audience: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-purple focus:border-transparent"
                      placeholder="e.g., Young professionals, Parents, Tech enthusiasts"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Platforms
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      {AVAILABLE_PLATFORMS.map((platform) => (
                        <label key={platform} className="flex items-center gap-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={formData.platforms.includes(platform)}
                            onChange={() => handlePlatformToggle(platform)}
                            className="rounded border-gray-300 text-primary-purple focus:ring-primary-purple"
                          />
                          <span className="text-sm text-gray-700">{platform}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-purple focus:border-transparent"
                      placeholder="Brief description of your brand"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Website
                    </label>
                    <input
                      type="url"
                      value={formData.website}
                      onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-purple focus:border-transparent"
                      placeholder="https://your-website.com"
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
                  <button
                    onClick={() => setShowForm(false)}
                    className="btn-secondary"
                    disabled={saving}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSaveBrand}
                    disabled={saving || !formData.name.trim()}
                    className="btn-primary flex items-center gap-2"
                  >
                    {saving ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <Save className="w-4 h-4" />
                    )}
                    {saving ? 'Saving...' : 'Save Brand'}
                  </button>
                </div>
              </div>
            ) : (
              <div className="p-6 flex items-center justify-center h-full">
                <div className="text-center text-gray-500">
                  <Building2 className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>Select a brand to edit or create a new one</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}