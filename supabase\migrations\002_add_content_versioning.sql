-- Add versioning support to content_calendars table
ALTER TABLE public.content_calendars 
ADD COLUMN version INTEGER DEFAULT 1,
ADD COLUMN is_current BOOLEAN DEFAULT true,
ADD COLUMN parent_version_id UUID REFERENCES public.content_calendars(id),
ADD COLUMN version_notes TEXT,
ADD COLUMN auto_saved BOOLEAN DEFAULT false;

-- Create index for version queries
CREATE INDEX IF NOT EXISTS idx_content_calendars_version ON public.content_calendars(brand_id, month, year, version);
CREATE INDEX IF NOT EXISTS idx_content_calendars_current ON public.content_calendars(brand_id, month, year, is_current);

-- Update the unique constraint to allow multiple versions
ALTER TABLE public.content_calendars DROP CONSTRAINT IF EXISTS content_calendars_brand_id_month_year_key;
ALTER TABLE public.content_calendars ADD CONSTRAINT content_calendars_brand_month_year_current_unique 
  UNIQUE(brand_id, month, year, is_current) DEFERRABLE INITIALLY DEFERRED;

-- <PERSON><PERSON> function to manage version history
CREATE OR REPLACE FUNCTION public.create_content_version()
R<PERSON>URNS TRIGGER AS $$
BEGIN
  -- If this is an update to current content, create a new version
  IF TG_OP = 'UPDATE' AND OLD.is_current = true AND NEW.is_current = true THEN
    -- Set old version to not current
    UPDATE public.content_calendars 
    SET is_current = false 
    WHERE id = OLD.id;
    
    -- Create new version
    INSERT INTO public.content_calendars (
      brand_id, user_id, month, year, content, version, is_current, 
      parent_version_id, version_notes, auto_saved
    ) VALUES (
      NEW.brand_id, NEW.user_id, NEW.month, NEW.year, NEW.content,
      COALESCE((
        SELECT MAX(version) + 1 
        FROM public.content_calendars 
        WHERE brand_id = NEW.brand_id AND month = NEW.month AND year = NEW.year
      ), 1),
      true,
      OLD.id,
      NEW.version_notes,
      NEW.auto_saved
    );
    
    -- Return NULL to prevent the original update
    RETURN NULL;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for version management
CREATE TRIGGER content_version_trigger
  BEFORE UPDATE ON public.content_calendars
  FOR EACH ROW
  EXECUTE FUNCTION public.create_content_version();

-- Create function to restore a version
CREATE OR REPLACE FUNCTION public.restore_content_version(
  p_version_id UUID,
  p_user_id UUID,
  p_notes TEXT DEFAULT 'Restored from previous version'
)
RETURNS UUID AS $$
DECLARE
  v_record RECORD;
  v_new_id UUID;
BEGIN
  -- Get the version to restore
  SELECT * INTO v_record
  FROM public.content_calendars
  WHERE id = p_version_id AND user_id = p_user_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Version not found or access denied';
  END IF;
  
  -- Set current version to not current
  UPDATE public.content_calendars
  SET is_current = false
  WHERE brand_id = v_record.brand_id 
    AND month = v_record.month 
    AND year = v_record.year 
    AND is_current = true
    AND user_id = p_user_id;
  
  -- Create new version from the restored content
  INSERT INTO public.content_calendars (
    brand_id, user_id, month, year, content, version, is_current,
    parent_version_id, version_notes, auto_saved
  ) VALUES (
    v_record.brand_id, v_record.user_id, v_record.month, v_record.year, 
    v_record.content,
    COALESCE((
      SELECT MAX(version) + 1 
      FROM public.content_calendars 
      WHERE brand_id = v_record.brand_id AND month = v_record.month AND year = v_record.year
    ), 1),
    true,
    p_version_id,
    p_notes,
    false
  ) RETURNING id INTO v_new_id;
  
  RETURN v_new_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to get version history
CREATE OR REPLACE FUNCTION public.get_content_version_history(
  p_brand_id UUID,
  p_user_id UUID,
  p_month INTEGER,
  p_year INTEGER
)
RETURNS TABLE (
  id UUID,
  version INTEGER,
  is_current BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  version_notes TEXT,
  auto_saved BOOLEAN,
  content_preview TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cc.id,
    cc.version,
    cc.is_current,
    cc.created_at,
    cc.version_notes,
    cc.auto_saved,
    CASE 
      WHEN cc.content IS NOT NULL THEN 
        CONCAT('Generated ', 
          (SELECT COUNT(*) FROM jsonb_array_elements(cc.content)), 
          ' posts for ', 
          array_to_string(
            (SELECT ARRAY(SELECT DISTINCT jsonb_extract_path_text(value, 'platform') 
             FROM jsonb_array_elements(cc.content))), 
            ', '
          )
        )
      ELSE 'No content'
    END as content_preview
  FROM public.content_calendars cc
  WHERE cc.brand_id = p_brand_id 
    AND cc.user_id = p_user_id
    AND cc.month = p_month 
    AND cc.year = p_year
  ORDER BY cc.version DESC;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up old versions (keep last 10 versions)
CREATE OR REPLACE FUNCTION public.cleanup_old_versions()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER := 0;
BEGIN
  WITH versions_to_delete AS (
    SELECT id
    FROM (
      SELECT id, 
        ROW_NUMBER() OVER (
          PARTITION BY brand_id, month, year 
          ORDER BY version DESC
        ) as rn
      FROM public.content_calendars
      WHERE is_current = false
    ) ranked
    WHERE rn > 10
  )
  DELETE FROM public.content_calendars
  WHERE id IN (SELECT id FROM versions_to_delete);
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Update RLS policies to include version access
DROP POLICY IF EXISTS "Users can view their own content calendars" ON public.content_calendars;
CREATE POLICY "Users can view their own content calendars" ON public.content_calendars
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own content calendars" ON public.content_calendars;
CREATE POLICY "Users can insert their own content calendars" ON public.content_calendars
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own content calendars" ON public.content_calendars;
CREATE POLICY "Users can update their own content calendars" ON public.content_calendars
  FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own content calendars" ON public.content_calendars;
CREATE POLICY "Users can delete their own content calendars" ON public.content_calendars
  FOR DELETE USING (auth.uid() = user_id);
