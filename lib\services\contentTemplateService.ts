// Content Templates and Variations Service
// Provides pre-built templates and variation generation for different industries and content types

export interface ContentTemplate {
  id: string
  name: string
  description: string
  category: 'educational' | 'promotional' | 'entertainment' | 'inspirational' | 'behind-the-scenes' | 'user-generated'
  industry: string[]
  platforms: string[]
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedEngagement: 'low' | 'medium' | 'high'
  template: {
    caption: string
    hashtags: string[]
    visualPrompt: string
    callToAction: string
  }
  variables: {
    [key: string]: {
      type: 'text' | 'select' | 'number'
      label: string
      options?: string[]
      placeholder?: string
      required: boolean
    }
  }
  examples: {
    platform: string
    preview: {
      caption: string
      hashtags: string[]
      visualPrompt: string
    }
  }[]
  tags: string[]
  createdBy: 'system' | 'user'
  usageCount: number
  rating: number
}

export interface TemplateVariation {
  id: string
  templateId: string
  name: string
  variables: Record<string, any>
  generatedContent: {
    caption: string
    hashtags: string[]
    visualPrompt: string
  }
  platform: string
  createdAt: Date
}

// Pre-built system templates
const SYSTEM_TEMPLATES: ContentTemplate[] = [
  // Educational Templates
  {
    id: 'edu-how-to-guide',
    name: 'How-To Guide',
    description: 'Step-by-step educational content that teaches your audience something valuable',
    category: 'educational',
    industry: ['technology', 'health-wellness', 'food-beverage', 'e-commerce'],
    platforms: ['instagram', 'tiktok', 'linkedin', 'twitter'],
    difficulty: 'beginner',
    estimatedEngagement: 'high',
    template: {
      caption: "🎯 How to {skill} in {timeframe}!\n\nStep 1: {step1}\nStep 2: {step2}\nStep 3: {step3}\n\n💡 Pro tip: {protip}\n\nTry this and let me know how it goes! 👇\n\n{cta}",
      hashtags: ['#{skill}', '#howto', '#tutorial', '#{industry}tips', '#learn{skill}', '#stepbystep'],
      visualPrompt: "Create a clean, educational infographic showing {skill} in {timeframe}. Include step-by-step visual elements with numbers 1, 2, 3. Use bright, engaging colors with plenty of white space. Show the final result prominently.",
      callToAction: "Save this post for later and tag someone who needs to see this!"
    },
    variables: {
      skill: { type: 'text', label: 'Skill/Topic', placeholder: 'create engaging content', required: true },
      timeframe: { type: 'select', label: 'Timeframe', options: ['5 minutes', '10 minutes', '30 minutes', '1 hour', '1 day', '1 week'], required: true },
      step1: { type: 'text', label: 'Step 1', placeholder: 'First action to take', required: true },
      step2: { type: 'text', label: 'Step 2', placeholder: 'Second action to take', required: true },
      step3: { type: 'text', label: 'Step 3', placeholder: 'Final action to take', required: true },
      protip: { type: 'text', label: 'Pro Tip', placeholder: 'Expert advice or shortcut', required: true },
      cta: { type: 'text', label: 'Call to Action', placeholder: 'What do you want users to do?', required: false }
    },
    examples: [
      {
        platform: 'instagram',
        preview: {
          caption: "🎯 How to create engaging content in 30 minutes!\n\nStep 1: Research trending topics in your niche\nStep 2: Create a compelling hook in the first 3 seconds\nStep 3: Add a clear call-to-action\n\n💡 Pro tip: Use the 80/20 rule - 80% value, 20% promotion\n\nTry this and let me know how it goes! 👇\n\nSave this post for later and tag someone who needs to see this!",
          hashtags: ['#contentcreation', '#howto', '#tutorial', '#marketingtips', '#learncontentcreation', '#stepbystep'],
          visualPrompt: "Create a clean, educational infographic showing content creation in 30 minutes. Include step-by-step visual elements with numbers 1, 2, 3. Use bright, engaging colors with plenty of white space. Show the final result prominently."
        }
      }
    ],
    tags: ['educational', 'tutorial', 'howto', 'stepbystep'],
    createdBy: 'system',
    usageCount: 0,
    rating: 4.8
  },

  // Promotional Templates
  {
    id: 'promo-product-launch',
    name: 'Product Launch Announcement',
    description: 'Generate excitement for new product launches with compelling copy and visuals',
    category: 'promotional',
    industry: ['e-commerce', 'technology', 'health-wellness', 'food-beverage'],
    platforms: ['instagram', 'tiktok', 'linkedin', 'twitter'],
    difficulty: 'intermediate',
    estimatedEngagement: 'high',
    template: {
      caption: "🚀 LAUNCHING NOW: {productName}!\n\n{productDescription}\n\n✨ What makes it special:\n• {feature1}\n• {feature2}\n• {feature3}\n\n🎉 Launch special: {offer}\n\n{cta}",
      hashtags: ['#{productName}', '#launch', '#new', '#{industry}', '#innovation', '#limitedtime'],
      visualPrompt: "Create an exciting product launch visual featuring {productName}. Show the product prominently with dynamic lighting and modern background. Include 'NEW' or 'LAUNCHING' text overlay. Use brand colors and create a sense of excitement and premium quality.",
      callToAction: "Link in bio to get yours now! Limited quantities available 🔥"
    },
    variables: {
      productName: { type: 'text', label: 'Product Name', placeholder: 'Amazing Widget Pro', required: true },
      productDescription: { type: 'text', label: 'Product Description', placeholder: 'Brief description of what it does', required: true },
      feature1: { type: 'text', label: 'Key Feature 1', placeholder: 'First major benefit', required: true },
      feature2: { type: 'text', label: 'Key Feature 2', placeholder: 'Second major benefit', required: true },
      feature3: { type: 'text', label: 'Key Feature 3', placeholder: 'Third major benefit', required: true },
      offer: { type: 'text', label: 'Launch Offer', placeholder: '20% off for first 100 customers', required: true },
      cta: { type: 'text', label: 'Call to Action', placeholder: 'What should users do next?', required: false }
    },
    examples: [
      {
        platform: 'instagram',
        preview: {
          caption: "🚀 LAUNCHING NOW: EcoClean Pro!\n\nThe revolutionary eco-friendly cleaning solution that's 10x more effective than traditional cleaners.\n\n✨ What makes it special:\n• 100% natural ingredients\n• Kills 99.9% of germs\n• Safe for kids and pets\n\n🎉 Launch special: 30% off for first 48 hours\n\nLink in bio to get yours now! Limited quantities available 🔥",
          hashtags: ['#EcoCleanPro', '#launch', '#new', '#ecofriendly', '#innovation', '#limitedtime'],
          visualPrompt: "Create an exciting product launch visual featuring EcoClean Pro. Show the product prominently with dynamic lighting and modern background. Include 'NEW' or 'LAUNCHING' text overlay. Use brand colors and create a sense of excitement and premium quality."
        }
      }
    ],
    tags: ['promotional', 'launch', 'product', 'announcement'],
    createdBy: 'system',
    usageCount: 0,
    rating: 4.6
  },

  // Behind-the-Scenes Templates
  {
    id: 'bts-process-reveal',
    name: 'Behind-the-Scenes Process',
    description: 'Show your audience the process behind your work to build trust and engagement',
    category: 'behind-the-scenes',
    industry: ['e-commerce', 'technology', 'health-wellness', 'food-beverage'],
    platforms: ['instagram', 'tiktok', 'linkedin'],
    difficulty: 'beginner',
    estimatedEngagement: 'medium',
    template: {
      caption: "👀 Behind the scenes: {processName}\n\nEver wondered how we {action}? Here's a peek into our {location}!\n\n🔍 What you're seeing:\n• {detail1}\n• {detail2}\n• {detail3}\n\n{insight}\n\n{cta}",
      hashtags: ['#behindthescenes', '#process', '#{industry}', '#transparency', '#howwedoit', '#team'],
      visualPrompt: "Create a behind-the-scenes photo or video showing {processName} in {location}. Include team members working, equipment or tools being used, and the process in action. Use natural lighting and authentic, candid moments. Show the workspace environment clearly.",
      callToAction: "What would you like to see behind the scenes next? Comment below! 👇"
    },
    variables: {
      processName: { type: 'text', label: 'Process Name', placeholder: 'creating our signature product', required: true },
      action: { type: 'text', label: 'Action/Activity', placeholder: 'make our products', required: true },
      location: { type: 'text', label: 'Location', placeholder: 'workshop/kitchen/studio', required: true },
      detail1: { type: 'text', label: 'Process Detail 1', placeholder: 'First step or interesting detail', required: true },
      detail2: { type: 'text', label: 'Process Detail 2', placeholder: 'Second step or detail', required: true },
      detail3: { type: 'text', label: 'Process Detail 3', placeholder: 'Third step or detail', required: true },
      insight: { type: 'text', label: 'Insight/Fun Fact', placeholder: 'Interesting fact about the process', required: true },
      cta: { type: 'text', label: 'Call to Action', placeholder: 'What do you want users to do?', required: false }
    },
    examples: [
      {
        platform: 'instagram',
        preview: {
          caption: "👀 Behind the scenes: creating our signature coffee blend\n\nEver wondered how we craft the perfect cup? Here's a peek into our roastery!\n\n🔍 What you're seeing:\n• Hand-selecting premium beans from 3 different regions\n• Small-batch roasting at exactly 425°F\n• Quality testing every 15 minutes\n\nFun fact: Each batch takes 18 minutes to reach perfection! ⏰\n\nWhat would you like to see behind the scenes next? Comment below! 👇",
          hashtags: ['#behindthescenes', '#process', '#coffee', '#transparency', '#howwedoit', '#team'],
          visualPrompt: "Create a behind-the-scenes photo or video showing creating our signature coffee blend in roastery. Include team members working, equipment or tools being used, and the process in action. Use natural lighting and authentic, candid moments. Show the workspace environment clearly."
        }
      }
    ],
    tags: ['behindthescenes', 'process', 'transparency', 'authentic'],
    createdBy: 'system',
    usageCount: 0,
    rating: 4.4
  },

  // Inspirational Templates
  {
    id: 'insp-motivation-monday',
    name: 'Motivational Monday',
    description: 'Start the week with inspiring content that motivates your audience',
    category: 'inspirational',
    industry: ['health-wellness', 'technology', 'e-commerce'],
    platforms: ['instagram', 'linkedin', 'twitter'],
    difficulty: 'beginner',
    estimatedEngagement: 'medium',
    template: {
      caption: "💪 Monday Motivation: {theme}\n\n{quote}\n\nRemember: {reminder}\n\n{personalStory}\n\nThis week, I challenge you to {challenge}\n\n{cta}",
      hashtags: ['#MondayMotivation', '#inspiration', '#mindset', '#{theme}', '#motivation', '#goals'],
      visualPrompt: "Create an inspirational image with {theme} theme. Include motivational typography with the quote overlaid on a beautiful background. Use uplifting colors like sunrise oranges, sky blues, or energetic greens. Add subtle textures or patterns for visual interest.",
      callToAction: "What's your Monday motivation? Share in the comments! 💬"
    },
    variables: {
      theme: { type: 'select', label: 'Theme', options: ['success', 'growth', 'perseverance', 'dreams', 'change', 'courage'], required: true },
      quote: { type: 'text', label: 'Inspirational Quote', placeholder: 'Your inspiring quote here', required: true },
      reminder: { type: 'text', label: 'Key Reminder', placeholder: 'Important message to remember', required: true },
      personalStory: { type: 'text', label: 'Personal Touch', placeholder: 'Brief personal story or connection', required: true },
      challenge: { type: 'text', label: 'Weekly Challenge', placeholder: 'Action for followers to take', required: true },
      cta: { type: 'text', label: 'Call to Action', placeholder: 'How should users engage?', required: false }
    },
    examples: [
      {
        platform: 'instagram',
        preview: {
          caption: "💪 Monday Motivation: Growth\n\n\"The only way to grow is to step outside your comfort zone.\"\n\nRemember: Every expert was once a beginner who refused to give up.\n\nLast year, I was terrified of public speaking. Today, I'm hosting webinars for hundreds of people. Growth happens when we embrace discomfort.\n\nThis week, I challenge you to do one thing that scares you but moves you closer to your goals.\n\nWhat's your Monday motivation? Share in the comments! 💬",
          hashtags: ['#MondayMotivation', '#inspiration', '#mindset', '#growth', '#motivation', '#goals'],
          visualPrompt: "Create an inspirational image with growth theme. Include motivational typography with the quote overlaid on a beautiful background. Use uplifting colors like sunrise oranges, sky blues, or energetic greens. Add subtle textures or patterns for visual interest."
        }
      }
    ],
    tags: ['inspirational', 'motivation', 'monday', 'mindset'],
    createdBy: 'system',
    usageCount: 0,
    rating: 4.5
  }
]

export class ContentTemplateService {
  private static templates: ContentTemplate[] = [...SYSTEM_TEMPLATES]

  /**
   * Get all available templates
   */
  static getAllTemplates(): ContentTemplate[] {
    return this.templates
  }

  /**
   * Get templates filtered by criteria
   */
  static getTemplates(filters: {
    category?: string
    industry?: string
    platform?: string
    difficulty?: string
    search?: string
  }): ContentTemplate[] {
    let filtered = this.templates

    if (filters.category) {
      filtered = filtered.filter(t => t.category === filters.category)
    }

    if (filters.industry) {
      filtered = filtered.filter(t => t.industry.includes(filters.industry!))
    }

    if (filters.platform) {
      filtered = filtered.filter(t => t.platforms.includes(filters.platform!))
    }

    if (filters.difficulty) {
      filtered = filtered.filter(t => t.difficulty === filters.difficulty)
    }

    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      filtered = filtered.filter(t =>
        t.name.toLowerCase().includes(searchLower) ||
        t.description.toLowerCase().includes(searchLower) ||
        t.tags.some(tag => tag.toLowerCase().includes(searchLower))
      )
    }

    return filtered.sort((a, b) => b.rating - a.rating)
  }

  /**
   * Get template by ID
   */
  static getTemplate(id: string): ContentTemplate | null {
    return this.templates.find(t => t.id === id) || null
  }

  /**
   * Generate content from template
   */
  static generateFromTemplate(
    templateId: string,
    variables: Record<string, any>,
    platform: string
  ): {
    caption: string
    hashtags: string[]
    visualPrompt: string
  } | null {
    const template = this.getTemplate(templateId)
    if (!template) return null

    // Replace variables in template
    let caption = template.template.caption
    let visualPrompt = template.template.visualPrompt
    let hashtags = [...template.template.hashtags]

    // Replace variables in caption
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{${key}}`
      caption = caption.replace(new RegExp(placeholder, 'g'), value)
      visualPrompt = visualPrompt.replace(new RegExp(placeholder, 'g'), value)

      // Replace in hashtags
      hashtags = hashtags.map(tag => tag.replace(new RegExp(placeholder, 'g'), value.toLowerCase().replace(/\s+/g, '')))
    })

    // Add platform-specific optimizations
    if (platform === 'twitter') {
      // Shorten caption for Twitter
      if (caption.length > 240) {
        caption = caption.substring(0, 237) + '...'
      }
      hashtags = hashtags.slice(0, 3) // Limit hashtags for Twitter
    } else if (platform === 'linkedin') {
      // Make more professional for LinkedIn
      caption = caption.replace(/🎯|🚀|💪|✨|🔥/g, '') // Remove some emojis
    }

    return {
      caption: caption.trim(),
      hashtags: hashtags.filter(tag => tag.length > 1), // Remove empty hashtags
      visualPrompt: visualPrompt.trim()
    }
  }

  /**
   * Create template variation
   */
  static createVariation(
    templateId: string,
    name: string,
    variables: Record<string, any>,
    platform: string
  ): TemplateVariation | null {
    const generatedContent = this.generateFromTemplate(templateId, variables, platform)
    if (!generatedContent) return null

    return {
      id: `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      templateId,
      name,
      variables,
      generatedContent,
      platform,
      createdAt: new Date()
    }
  }

  /**
   * Get template categories
   */
  static getCategories(): string[] {
    const categories = new Set(this.templates.map(t => t.category))
    return Array.from(categories)
  }

  /**
   * Get supported industries
   */
  static getIndustries(): string[] {
    const industries = new Set(this.templates.flatMap(t => t.industry))
    return Array.from(industries)
  }

  /**
   * Get template usage statistics
   */
  static getUsageStats(): {
    totalTemplates: number
    categoryCounts: Record<string, number>
    industryUsage: Record<string, number>
    averageRating: number
  } {
    const categoryCounts: Record<string, number> = {}
    const industryUsage: Record<string, number> = {}
    let totalRating = 0

    this.templates.forEach(template => {
      categoryCounts[template.category] = (categoryCounts[template.category] || 0) + 1

      template.industry.forEach(industry => {
        industryUsage[industry] = (industryUsage[industry] || 0) + template.usageCount
      })

      totalRating += template.rating
    })

    return {
      totalTemplates: this.templates.length,
      categoryCounts,
      industryUsage,
      averageRating: totalRating / this.templates.length
    }
  }

  /**
   * Add custom template (for future user-generated templates)
   */
  static addCustomTemplate(template: Omit<ContentTemplate, 'id' | 'createdBy' | 'usageCount' | 'rating'>): ContentTemplate {
    const newTemplate: ContentTemplate = {
      ...template,
      id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdBy: 'user',
      usageCount: 0,
      rating: 0
    }

    this.templates.push(newTemplate)
    return newTemplate
  }

  /**
   * Update template usage count
   */
  static incrementUsage(templateId: string): void {
    const template = this.templates.find(t => t.id === templateId)
    if (template) {
      template.usageCount++
    }
  }
}
