export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type SubscriptionStatus = 'active' | 'canceled' | 'past_due' | 'unpaid' | 'trialing'
export type SubscriptionTier = 'free' | 'starter' | 'professional' | 'enterprise'
export type ContentStatus = 'draft' | 'scheduled' | 'published' | 'archived'
export type NotificationType = 'system' | 'billing' | 'content' | 'feature'
export type AuditAction = 'create' | 'update' | 'delete' | 'login' | 'logout'

export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          company_name: string | null
          job_title: string | null
          phone: string | null
          timezone: string
          language: string
          onboarding_completed: boolean
          email_notifications: boolean
          marketing_emails: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          company_name?: string | null
          job_title?: string | null
          phone?: string | null
          timezone?: string
          language?: string
          onboarding_completed?: boolean
          email_notifications?: boolean
          marketing_emails?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          company_name?: string | null
          job_title?: string | null
          phone?: string | null
          timezone?: string
          language?: string
          onboarding_completed?: boolean
          email_notifications?: boolean
          marketing_emails?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      subscriptions: {
        Row: {
          id: string
          user_id: string
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          status: SubscriptionStatus
          tier: SubscriptionTier
          current_period_start: string | null
          current_period_end: string | null
          trial_end: string | null
          cancel_at_period_end: boolean
          canceled_at: string | null
          metadata: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          status?: SubscriptionStatus
          tier?: SubscriptionTier
          current_period_start?: string | null
          current_period_end?: string | null
          trial_end?: string | null
          cancel_at_period_end?: boolean
          canceled_at?: string | null
          metadata?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          status?: SubscriptionStatus
          tier?: SubscriptionTier
          current_period_start?: string | null
          current_period_end?: string | null
          trial_end?: string | null
          cancel_at_period_end?: boolean
          canceled_at?: string | null
          metadata?: Json
          created_at?: string
          updated_at?: string
        }
      }
      usage_tracking: {
        Row: {
          id: string
          user_id: string
          subscription_id: string | null
          resource_type: string
          amount: number
          metadata: Json
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          subscription_id?: string | null
          resource_type: string
          amount?: number
          metadata?: Json
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          subscription_id?: string | null
          resource_type?: string
          amount?: number
          metadata?: Json
          created_at?: string
        }
      }
      brands: {
        Row: {
          id: string
          user_id: string
          name: string
          industry: string
          tone: string
          target_audience: string
          platforms: string[]
          description: string | null
          website: string | null
          logo_url: string | null
          brand_colors: Json
          brand_fonts: Json
          brand_voice_guidelines: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          industry: string
          tone: string
          target_audience: string
          platforms?: string[]
          description?: string | null
          website?: string | null
          logo_url?: string | null
          brand_colors?: Json
          brand_fonts?: Json
          brand_voice_guidelines?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          industry?: string
          tone?: string
          target_audience?: string
          platforms?: string[]
          description?: string | null
          website?: string | null
          logo_url?: string | null
          brand_colors?: Json
          brand_fonts?: Json
          brand_voice_guidelines?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      content_calendars: {
        Row: {
          id: string
          brand_id: string
          user_id: string
          month: number
          year: number
          content: Json
          version: number
          is_current: boolean
          parent_version_id?: string
          version_notes?: string
          auto_saved: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          brand_id: string
          user_id: string
          month: number
          year: number
          content: Json
          version?: number
          is_current?: boolean
          parent_version_id?: string
          version_notes?: string
          auto_saved?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          brand_id?: string
          user_id?: string
          month?: number
          year?: number
          content?: Json
          version?: number
          is_current?: boolean
          parent_version_id?: string
          version_notes?: string
          auto_saved?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      content_posts: {
        Row: {
          id: string
          calendar_id: string
          brand_id: string
          user_id: string
          platform: string
          content_type: string
          title: string | null
          caption: string
          hashtags: string[]
          visual_prompt: string | null
          media_urls: string[]
          scheduled_date: string | null
          published_date: string | null
          status: ContentStatus
          engagement_data: Json
          ai_model_used: string | null
          generation_prompt: string | null
          viral_score: number | null
          performance_metrics: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          calendar_id: string
          brand_id: string
          user_id: string
          platform: string
          content_type: string
          title?: string | null
          caption: string
          hashtags?: string[]
          visual_prompt?: string | null
          media_urls?: string[]
          scheduled_date?: string | null
          published_date?: string | null
          status?: ContentStatus
          engagement_data?: Json
          ai_model_used?: string | null
          generation_prompt?: string | null
          viral_score?: number | null
          performance_metrics?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          calendar_id?: string
          brand_id?: string
          user_id?: string
          platform?: string
          content_type?: string
          title?: string | null
          caption?: string
          hashtags?: string[]
          visual_prompt?: string | null
          media_urls?: string[]
          scheduled_date?: string | null
          published_date?: string | null
          status?: ContentStatus
          engagement_data?: Json
          ai_model_used?: string | null
          generation_prompt?: string | null
          viral_score?: number | null
          performance_metrics?: Json
          created_at?: string
          updated_at?: string
        }
      }
      notifications: {
        Row: {
          id: string
          user_id: string
          type: NotificationType
          title: string
          message: string
          action_url: string | null
          is_read: boolean
          metadata: Json
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          type: NotificationType
          title: string
          message: string
          action_url?: string | null
          is_read?: boolean
          metadata?: Json
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          type?: NotificationType
          title?: string
          message?: string
          action_url?: string | null
          is_read?: boolean
          metadata?: Json
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      check_usage_limit: {
        Args: {
          p_user_id: string
          p_resource_type: string
          p_period_days?: number
        }
        Returns: Json
      }
      get_user_feature_flags: {
        Args: {
          p_user_id: string
        }
        Returns: {
          name: string
          is_enabled: boolean
        }[]
      }
      track_usage: {
        Args: {
          p_user_id: string
          p_resource_type: string
          p_amount?: number
          p_metadata?: Json
        }
        Returns: string
      }
    }
    Enums: {
      subscription_status: SubscriptionStatus
      subscription_tier: SubscriptionTier
      content_status: ContentStatus
      notification_type: NotificationType
      audit_action: AuditAction
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}