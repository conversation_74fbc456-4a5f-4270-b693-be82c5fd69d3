'use client'

import { useState, useEffect } from 'react'
import { X, ArrowRight, ArrowLeft, CheckCircle, Instagram, Linkedin, Twitter } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { brandService } from '@/lib/services/brandService'
import { toast } from 'react-hot-toast'
import { createClient } from '@/lib/supabase/client'

interface OnboardingModalProps {
  onClose: () => void
  onBrandCreated?: () => void
}

interface BrandData {
  brandName: string
  industry: string
  tone: string
  targetAudience: string
  platforms: string[]
  description: string
  website: string
}

export default function OnboardingModal({ onClose, onBrandCreated }: OnboardingModalProps) {
  const router = useRouter()
  const supabase = createClient()
  const [step, setStep] = useState(1)
  const [isCreating, setIsCreating] = useState(false)
  const [userId, setUserId] = useState<string | null>(null)
  const [brandData, setBrandData] = useState<BrandData>({
    brandName: '',
    industry: '',
    tone: '',
    targetAudience: '',
    platforms: [],
    description: '',
    website: ''
  })

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        setUserId(user.id)
      }
    }
    getUser()
  }, [])

  const industries = [
    'E-commerce', 'SaaS/Technology', 'Health & Wellness', 'Fashion & Beauty',
    'Food & Beverage', 'Education', 'Real Estate', 'Finance', 'Travel',
    'Entertainment', 'Non-profit', 'Other'
  ]

  const tones = [
    { value: 'professional', label: 'Professional', description: 'Formal, authoritative, and business-focused' },
    { value: 'casual', label: 'Casual', description: 'Friendly, approachable, and conversational' },
    { value: 'playful', label: 'Playful', description: 'Fun, energetic, and creative' },
    { value: 'inspirational', label: 'Inspirational', description: 'Motivating, uplifting, and empowering' },
    { value: 'educational', label: 'Educational', description: 'Informative, helpful, and instructive' }
  ]

  const platforms = [
    { id: 'instagram', name: 'Instagram', icon: <Instagram className="w-6 h-6" />, color: 'from-pink-500 to-purple-500' },
    { id: 'tiktok', name: 'TikTok', icon: <div className="w-6 h-6 bg-black rounded-full" />, color: 'from-black to-gray-800' },
    { id: 'linkedin', name: 'LinkedIn', icon: <Linkedin className="w-6 h-6" />, color: 'from-blue-600 to-blue-700' },
    { id: 'twitter', name: 'X/Twitter', icon: <Twitter className="w-6 h-6" />, color: 'from-gray-800 to-black' }
  ]

  const handlePlatformToggle = (platformId: string) => {
    setBrandData(prev => ({
      ...prev,
      platforms: prev.platforms.includes(platformId)
        ? prev.platforms.filter(p => p !== platformId)
        : [...prev.platforms, platformId]
    }))
  }

  const handleNext = async () => {
    if (step < 3) {
      setStep(step + 1)
    } else {
      // Create brand in Supabase
      setIsCreating(true)
      try {
        if (!userId) {
          throw new Error('User not authenticated')
        }

        await brandService.createBrand(userId, {
          name: brandData.brandName,
          industry: brandData.industry,
          tone: brandData.tone,
          target_audience: brandData.targetAudience,
          platforms: brandData.platforms,
          description: brandData.description,
          website: brandData.website
        })

        toast.success('Brand created successfully!')
        onBrandCreated?.()
        onClose()
      } catch (error) {
        console.error('Error creating brand:', error)
        toast.error('Failed to create brand. Please try again.')
      } finally {
        setIsCreating(false)
      }
    }
  }

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  const isStepValid = () => {
    switch (step) {
      case 1:
        return brandData.brandName && brandData.industry && brandData.targetAudience
      case 2:
        return brandData.tone
      case 3:
        return brandData.platforms.length > 0
      default:
        return false
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-heading font-bold text-neutral-dark">
              Set Up Your Brand
            </h2>
            <p className="text-gray-600 mt-1">
              Step {step} of 3 - Let's personalize your content strategy
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-150"
          >
            <X className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Progress Bar */}
        <div className="px-6 py-4">
          <div className="flex items-center gap-2">
            {[1, 2, 3].map((stepNumber) => (
              <div key={stepNumber} className="flex items-center flex-1">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${stepNumber <= step
                  ? 'bg-gradient-primary text-white'
                  : 'bg-gray-200 text-gray-500'
                  }`}>
                  {stepNumber < step ? <CheckCircle className="w-5 h-5" /> : stepNumber}
                </div>
                {stepNumber < 3 && (
                  <div className={`flex-1 h-1 mx-2 rounded ${stepNumber < step ? 'bg-gradient-primary' : 'bg-gray-200'
                    }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 1 && (
            <div className="space-y-6">
              <div>
                <h3 className="text-xl font-semibold text-neutral-dark mb-4">
                  Tell us about your brand
                </h3>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Brand Name *
                </label>
                <input
                  type="text"
                  value={brandData.brandName}
                  onChange={(e) => setBrandData(prev => ({ ...prev, brandName: e.target.value }))}
                  className="form-input"
                  placeholder="Enter your brand or business name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Industry *
                </label>
                <select
                  value={brandData.industry}
                  onChange={(e) => setBrandData(prev => ({ ...prev, industry: e.target.value }))}
                  className="form-input"
                >
                  <option value="">Select your industry</option>
                  {industries.map((industry) => (
                    <option key={industry} value={industry}>{industry}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Target Audience *
                </label>
                <textarea
                  value={brandData.targetAudience}
                  onChange={(e) => setBrandData(prev => ({ ...prev, targetAudience: e.target.value }))}
                  className="form-textarea h-24"
                  placeholder="Describe your target audience (e.g., young professionals aged 25-35 interested in productivity and wellness)"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Brand Description
                </label>
                <textarea
                  value={brandData.description}
                  onChange={(e) => setBrandData(prev => ({ ...prev, description: e.target.value }))}
                  className="form-textarea h-20"
                  placeholder="Brief description of your brand (optional)"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Website
                </label>
                <input
                  type="url"
                  value={brandData.website}
                  onChange={(e) => setBrandData(prev => ({ ...prev, website: e.target.value }))}
                  className="form-input"
                  placeholder="https://yourwebsite.com (optional)"
                />
              </div>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-6">
              <div>
                <h3 className="text-xl font-semibold text-neutral-dark mb-2">
                  Choose your brand tone
                </h3>
                <p className="text-gray-600 mb-6">
                  This will influence how AI generates your content
                </p>
              </div>

              <div className="grid gap-4">
                {tones.map((tone) => (
                  <button
                    key={tone.value}
                    onClick={() => setBrandData(prev => ({ ...prev, tone: tone.value }))}
                    className={`p-4 border-2 rounded-lg text-left transition-all duration-150 ${brandData.tone === tone.value
                      ? 'border-primary-purple bg-primary-purple/5'
                      : 'border-gray-200 hover:border-gray-300'
                      }`}
                  >
                    <div className="font-semibold text-neutral-dark mb-1">
                      {tone.label}
                    </div>
                    <div className="text-sm text-gray-600">
                      {tone.description}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {step === 3 && (
            <div className="space-y-6">
              <div>
                <h3 className="text-xl font-semibold text-neutral-dark mb-2">
                  Select your platforms
                </h3>
                <p className="text-gray-600 mb-6">
                  Choose which social media platforms you want to create content for
                </p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {platforms.map((platform) => (
                  <button
                    key={platform.id}
                    onClick={() => handlePlatformToggle(platform.id)}
                    className={`p-6 border-2 rounded-lg transition-all duration-150 ${brandData.platforms.includes(platform.id)
                      ? 'border-primary-purple bg-primary-purple/5'
                      : 'border-gray-200 hover:border-gray-300'
                      }`}
                  >
                    <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${platform.color} flex items-center justify-center text-white mb-3 mx-auto`}>
                      {platform.icon}
                    </div>
                    <div className="font-semibold text-neutral-dark">
                      {platform.name}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <button
            onClick={handleBack}
            disabled={step === 1}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-150 ${step === 1
              ? 'text-gray-400 cursor-not-allowed'
              : 'text-gray-600 hover:text-primary-purple'
              }`}
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </button>

          <button
            onClick={handleNext}
            disabled={!isStepValid() || isCreating}
            className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-150 ${isStepValid() && !isCreating
              ? 'btn-primary'
              : 'bg-gray-200 text-gray-400 cursor-not-allowed'
              }`}
          >
            {isCreating ? 'Creating...' : step === 3 ? 'Create Brand' : 'Next'}
            <ArrowRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  )
}