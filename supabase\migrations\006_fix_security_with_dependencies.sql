-- Fix Security Warnings - Corrected Version with Dependency Handling
-- This migration properly handles trigger dependencies when updating functions

-- 1. Fix update_updated_at_column function (has trigger dependencies)
-- We need to use CREATE OR REPLACE instead of DROP/CREATE
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- 2. Fix handle_new_user function (may have trigger dependencies)
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    INSERT INTO public.user_profiles (
        id,
        email,
        full_name,
        avatar_url,
        timezone,
        language,
        onboarding_completed,
        email_notifications,
        marketing_emails
    ) VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'avatar_url', ''),
        COALESCE(NEW.raw_user_meta_data->>'timezone', 'UTC'),
        COALESCE(NEW.raw_user_meta_data->>'language', 'en'),
        false,
        true,
        false
    );
    RETURN NEW;
END;
$$;

-- 3. Fix log_audit_event function
CREATE OR REPLACE FUNCTION public.log_audit_event(
    p_user_id UUID,
    p_action TEXT,
    p_resource_type TEXT,
    p_details JSONB DEFAULT '{}'::jsonb
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    INSERT INTO public.audit_logs (
        user_id,
        action,
        resource_type,
        details,
        ip_address,
        user_agent
    ) VALUES (
        p_user_id,
        p_action,
        p_resource_type,
        p_details,
        COALESCE(current_setting('request.headers', true)::json->>'x-forwarded-for', 'unknown'),
        COALESCE(current_setting('request.headers', true)::json->>'user-agent', 'unknown')
    );
END;
$$;

-- 4. Fix track_usage function
CREATE OR REPLACE FUNCTION public.track_usage(
    p_user_id UUID,
    p_feature TEXT,
    p_usage_count INTEGER DEFAULT 1
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    INSERT INTO public.usage_tracking (
        user_id,
        feature,
        usage_count,
        date
    ) VALUES (
        p_user_id,
        p_feature,
        p_usage_count,
        CURRENT_DATE
    )
    ON CONFLICT (user_id, feature, date)
    DO UPDATE SET
        usage_count = public.usage_tracking.usage_count + p_usage_count,
        updated_at = NOW();
END;
$$;

-- 5. Fix check_usage_limit function
CREATE OR REPLACE FUNCTION public.check_usage_limit(
    p_user_id UUID,
    p_feature TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    current_usage INTEGER;
    user_tier TEXT;
    tier_limit INTEGER;
BEGIN
    -- Get user's subscription tier
    SELECT s.tier INTO user_tier
    FROM public.subscriptions s
    WHERE s.user_id = p_user_id AND s.status = 'active'
    ORDER BY s.created_at DESC
    LIMIT 1;
    
    -- Default to free tier if no subscription found
    user_tier := COALESCE(user_tier, 'free');
    
    -- Get current month usage
    SELECT COALESCE(SUM(usage_count), 0) INTO current_usage
    FROM public.usage_tracking
    WHERE user_id = p_user_id 
    AND feature = p_feature 
    AND date >= date_trunc('month', CURRENT_DATE);
    
    -- Set limits based on tier and feature
    CASE 
        WHEN user_tier = 'free' AND p_feature = 'calendar_generation' THEN tier_limit := 3;
        WHEN user_tier = 'starter' AND p_feature = 'calendar_generation' THEN tier_limit := 10;
        WHEN user_tier = 'professional' AND p_feature = 'calendar_generation' THEN tier_limit := 50;
        WHEN user_tier = 'enterprise' THEN tier_limit := 999999; -- Unlimited
        ELSE tier_limit := 1; -- Default conservative limit
    END CASE;
    
    RETURN current_usage < tier_limit;
END;
$$;

-- 6. Fix create_notification function
CREATE OR REPLACE FUNCTION public.create_notification(
    p_user_id UUID,
    p_type TEXT,
    p_title TEXT,
    p_message TEXT,
    p_metadata JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO public.notifications (
        user_id,
        type,
        title,
        message,
        metadata,
        is_read
    ) VALUES (
        p_user_id,
        p_type,
        p_title,
        p_message,
        p_metadata,
        false
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$;

-- 7. Fix get_user_feature_flags function
CREATE OR REPLACE FUNCTION public.get_user_feature_flags(p_user_id UUID)
RETURNS TABLE(flag_name TEXT, is_enabled BOOLEAN, config JSONB)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT
        ff.flag_name,
        ff.is_enabled,
        ff.config
    FROM public.feature_flags ff
    WHERE ff.is_global = true
       OR ff.user_id = p_user_id
    ORDER BY ff.flag_name;
END;
$$;

-- 8. Fix audit_brands_changes function (has trigger dependencies)
CREATE OR REPLACE FUNCTION public.audit_brands_changes()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM public.log_audit_event(
            NEW.user_id,
            'create',
            'brand',
            jsonb_build_object('brand_id', NEW.id, 'brand_name', NEW.name)
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        PERFORM public.log_audit_event(
            NEW.user_id,
            'update',
            'brand',
            jsonb_build_object('brand_id', NEW.id, 'brand_name', NEW.name, 'changes', to_jsonb(NEW) - to_jsonb(OLD))
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM public.log_audit_event(
            OLD.user_id,
            'delete',
            'brand',
            jsonb_build_object('brand_id', OLD.id, 'brand_name', OLD.name)
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$;

-- 9. Fix handle_updated_at function (may have trigger dependencies)
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- 10. Fix ensure_single_active_brand function (may have trigger dependencies)
CREATE OR REPLACE FUNCTION public.ensure_single_active_brand()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- If this brand is being set as active, deactivate all other brands for this user
    IF NEW.is_active = true THEN
        UPDATE public.brands
        SET is_active = false, updated_at = NOW()
        WHERE user_id = NEW.user_id
        AND id != NEW.id
        AND is_active = true;
    END IF;

    RETURN NEW;
END;
$$;
