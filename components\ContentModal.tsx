'use client'

import { useState, useCallback } from 'react'
import { X, Save, Clock, Hash, Image, Copy, Instagram, Linkedin, Twitter, CheckCircle, Zap } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { useAutoSave } from '@/lib/hooks/useDebounce'
import ContentVariations from './ContentVariations'

interface ContentItem {
  id: string
  date: string
  platform: string
  type: string
  caption: string
  hashtags: string[]
  visualPrompt: string
  bestTime: string
}

interface ContentModalProps {
  content: ContentItem
  onClose: () => void
  onUpdate: (content: ContentItem, isAutoSave?: boolean) => void
  brandData?: {
    brandName: string
    industry: string
    tone: string
    targetAudience: string
    platforms: string[]
  }
}

const platformIcons = {
  instagram: <Instagram className="w-5 h-5" />,
  tiktok: <div className="w-5 h-5 bg-black rounded-full" />,
  linkedin: <Linkedin className="w-5 h-5" />,
  twitter: <Twitter className="w-5 h-5" />
}

const platformColors = {
  instagram: 'from-pink-500 to-purple-500',
  tiktok: 'from-black to-gray-800',
  linkedin: 'from-blue-600 to-blue-700',
  twitter: 'from-gray-800 to-black'
}

export default function ContentModal({ content, onClose, onUpdate, brandData }: ContentModalProps) {
  const [editedContent, setEditedContent] = useState<ContentItem>(content)
  const [isEditing, setIsEditing] = useState(false)
  const [showVariations, setShowVariations] = useState(false)

  // Auto-save callback
  const handleAutoSave = useCallback(async (updatedContent: ContentItem) => {
    if (isEditing) {
      await onUpdate(updatedContent, true) // true indicates auto-save
    }
  }, [onUpdate, isEditing])

  // Use auto-save hook with 2-second delay
  const { isSaving } = useAutoSave(editedContent, handleAutoSave, 2000, isEditing)

  const handleSave = () => {
    onUpdate(editedContent, false) // false indicates manual save
    setIsEditing(false)
    toast.success('Content updated successfully!')
  }

  const handleVariationSelect = (variation: ContentItem) => {
    setEditedContent(variation)
    onUpdate(variation, false)
    setShowVariations(false)
  }

  const handleCopyCaption = () => {
    navigator.clipboard.writeText(editedContent.caption)
    toast.success('Caption copied to clipboard!')
  }

  const handleCopyHashtags = () => {
    navigator.clipboard.writeText(editedContent.hashtags.join(' '))
    toast.success('Hashtags copied to clipboard!')
  }

  const handleHashtagsChange = (value: string) => {
    const hashtags = value.split(' ').filter(tag => tag.trim() !== '')
    setEditedContent(prev => ({ ...prev, hashtags }))
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${platformColors[content.platform as keyof typeof platformColors]} flex items-center justify-center text-white`}>
              {platformIcons[content.platform as keyof typeof platformIcons]}
            </div>
            <div>
              <h2 className="text-xl font-heading font-bold text-neutral-dark capitalize">
                {content.platform} Post
              </h2>
              <p className="text-gray-600 text-sm">
                {formatDate(content.date)}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Auto-save indicator */}
            {isEditing && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-purple"></div>
                    <span>Saving...</span>
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Auto-saved</span>
                  </>
                )}
              </div>
            )}

            {isEditing ? (
              <>
                <button
                  onClick={() => setIsEditing(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors duration-150"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  className="btn-primary flex items-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  Save Changes
                </button>
              </>
            ) : (
              <>
                {brandData && (
                  <button
                    onClick={() => setShowVariations(true)}
                    className="btn-secondary flex items-center gap-2"
                  >
                    <Zap className="w-4 h-4" />
                    Variations
                  </button>
                )}
                <button
                  onClick={() => setIsEditing(true)}
                  className="btn-secondary"
                >
                  Edit Content
                </button>
              </>
            )}
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-150"
            >
              <X className="w-6 h-6 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Content Details */}
            <div className="space-y-6">
              {/* Post Caption */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-700">
                    Post Caption
                  </label>
                  <button
                    onClick={handleCopyCaption}
                    className="flex items-center gap-1 text-xs text-primary-purple hover:text-primary-blue transition-colors duration-150"
                  >
                    <Copy className="w-3 h-3" />
                    Copy
                  </button>
                </div>
                {isEditing ? (
                  <textarea
                    value={editedContent.caption}
                    onChange={(e) => setEditedContent(prev => ({ ...prev, caption: e.target.value }))}
                    className="form-textarea h-32"
                    placeholder="Enter your post caption..."
                  />
                ) : (
                  <div className="p-4 bg-gray-50 rounded-lg border">
                    <p className="text-gray-800 leading-relaxed whitespace-pre-wrap">
                      {editedContent.caption}
                    </p>
                  </div>
                )}
              </div>

              {/* Hashtags */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-700">
                    Hashtags
                  </label>
                  <button
                    onClick={handleCopyHashtags}
                    className="flex items-center gap-1 text-xs text-primary-purple hover:text-primary-blue transition-colors duration-150"
                  >
                    <Copy className="w-3 h-3" />
                    Copy
                  </button>
                </div>
                {isEditing ? (
                  <input
                    type="text"
                    value={editedContent.hashtags.join(' ')}
                    onChange={(e) => handleHashtagsChange(e.target.value)}
                    className="form-input"
                    placeholder="#hashtag1 #hashtag2 #hashtag3"
                  />
                ) : (
                  <div className="p-4 bg-gray-50 rounded-lg border">
                    <div className="flex flex-wrap gap-2">
                      {editedContent.hashtags.map((hashtag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center gap-1 px-2 py-1 bg-primary-purple/10 text-primary-purple rounded-md text-sm"
                        >
                          <Hash className="w-3 h-3" />
                          {hashtag.replace('#', '')}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Best Posting Time */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Best Posting Time
                </label>
                {isEditing ? (
                  <input
                    type="time"
                    value={editedContent.bestTime}
                    onChange={(e) => setEditedContent(prev => ({ ...prev, bestTime: e.target.value }))}
                    className="form-input"
                  />
                ) : (
                  <div className="flex items-center gap-2 p-4 bg-gray-50 rounded-lg border">
                    <Clock className="w-5 h-5 text-primary-purple" />
                    <span className="text-gray-800 font-medium">
                      {editedContent.bestTime}
                    </span>
                    <span className="text-gray-600 text-sm">
                      (Optimal engagement time)
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Right Column - Visual Prompt */}
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Visual Content Prompt
                </label>
                {isEditing ? (
                  <textarea
                    value={editedContent.visualPrompt}
                    onChange={(e) => setEditedContent(prev => ({ ...prev, visualPrompt: e.target.value }))}
                    className="form-textarea h-40"
                    placeholder="Describe the visual content for this post..."
                  />
                ) : (
                  <div className="p-4 bg-gradient-to-br from-primary-purple/5 to-primary-blue/5 rounded-lg border border-primary-purple/20">
                    <div className="flex items-start gap-3">
                      <Image className="w-5 h-5 text-primary-purple mt-1 flex-shrink-0" />
                      <p className="text-gray-800 leading-relaxed">
                        {editedContent.visualPrompt}
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Content Type & Platform Info */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Content Type
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    <span className="text-gray-800 font-medium capitalize">
                      {editedContent.type}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Platform
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    <div className="flex items-center gap-2">
                      {platformIcons[editedContent.platform as keyof typeof platformIcons]}
                      <span className="text-gray-800 font-medium capitalize">
                        {editedContent.platform}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* AI Tips */}
              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="font-medium text-blue-900 mb-2">💡 AI Tips for {editedContent.platform}</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  {editedContent.platform === 'instagram' && (
                    <>
                      <li>• Use 3-5 relevant hashtags for better reach</li>
                      <li>• Post during 11 AM - 1 PM for peak engagement</li>
                      <li>• Include a clear call-to-action</li>
                    </>
                  )}
                  {editedContent.platform === 'linkedin' && (
                    <>
                      <li>• Professional tone works best</li>
                      <li>• Post on Tuesday-Thursday for B2B audience</li>
                      <li>• Include industry insights</li>
                    </>
                  )}
                  {editedContent.platform === 'twitter' && (
                    <>
                      <li>• Keep it concise and engaging</li>
                      <li>• Use trending hashtags sparingly</li>
                      <li>• Engage with replies quickly</li>
                    </>
                  )}
                  {editedContent.platform === 'tiktok' && (
                    <>
                      <li>• Hook viewers in first 3 seconds</li>
                      <li>• Use trending sounds and effects</li>
                      <li>• Post during 6-10 PM for best reach</li>
                    </>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Variations Modal */}
      {showVariations && brandData && (
        <ContentVariations
          brandData={brandData}
          date={new Date(editedContent.date)}
          platform={editedContent.platform}
          onClose={() => setShowVariations(false)}
          onSelectVariation={handleVariationSelect}
        />
      )}
    </div>
  )
}