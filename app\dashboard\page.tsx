'use client'

import { useState, useEffect, useMemo } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { Plus } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'
import type { Database } from '@/lib/supabase/database.types'

// Services
import { generateCalendarContent, generateDemoCalendarContent } from '@/lib/ai-service'
import { brandService } from '@/lib/services/brandService'
import { contentCalendarService } from '@/lib/services/contentCalendarService'
import { safeAsyncOperation, handleApiError } from '@/lib/utils/errorHandling'

// Components
import DashboardHeader from '@/components/dashboard/DashboardHeader'
import DashboardSidebar from '@/components/dashboard/DashboardSidebar'
import DashboardMainContent from '@/components/dashboard/DashboardMainContent'
import DashboardModals from '@/components/dashboard/DashboardModals'
import PostingTimeRecommendations from '@/components/PostingTimeRecommendations'
import TemplateLibrary from '@/components/TemplateLibrary'
import TemplateGenerator from '@/components/TemplateGenerator'
import BulkOperations from '@/components/BulkOperations'
import { ContentItem, Brand } from '@/lib/types/content'

type DatabaseBrand = Database['public']['Tables']['brands']['Row']

interface BrandData {
  name: string
  industry: string
  tone: string
  targetAudience: string
  platforms: string[]
  description: string
  website: string
}

// ContentItem is imported from '@/lib/types/content'

// Adapter function to convert DatabaseBrand to Brand
const adaptDatabaseBrandToBrand = (dbBrand: DatabaseBrand): Brand => ({
  id: dbBrand.id,
  name: dbBrand.name,
  industry: dbBrand.industry,
  tone: dbBrand.tone,
  target_audience: dbBrand.target_audience,
  platforms: dbBrand.platforms,
  description: dbBrand.description || '',
  website: dbBrand.website || ''
})

export default function DashboardPage() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [brandData, setBrandData] = useState<BrandData | null>(null)
  const [calendarContent, setCalendarContent] = useState<ContentItem[]>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('viralpath-calendar-content')
      return saved ? JSON.parse(saved) : []
    }
    return []
  })
  const [selectedContent, setSelectedContent] = useState<ContentItem | null>(null)
  const [showContentModal, setShowContentModal] = useState(false)
  const [showExportModal, setShowExportModal] = useState(false)
  const [showUserProfile, setShowUserProfile] = useState(false)
  const [showBrandManager, setShowBrandManager] = useState(false)
  const [currentBrand, setCurrentBrand] = useState<DatabaseBrand | null>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('viralpath-current-brand')
      return saved ? JSON.parse(saved) : null
    }
    return null
  })
  const [brands, setBrands] = useState<DatabaseBrand[]>([])
  const [isGenerating, setIsGenerating] = useState(false)
  const [currentMonth, setCurrentMonth] = useState<Date | null>(() => {
    // Initialize with current month to avoid hydration issues
    if (typeof window !== 'undefined') {
      const now = new Date()
      return new Date(now.getFullYear(), now.getMonth(), 1)
    }
    return null
  })
  const [error, setError] = useState<string | null>(null)
  const [isLoadingContent, setIsLoadingContent] = useState(false)
  const [showVersionHistory, setShowVersionHistory] = useState(false)
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showPostingTimes, setShowPostingTimes] = useState(false)
  const [showTemplateLibrary, setShowTemplateLibrary] = useState(false)
  const [showTemplateGenerator, setShowTemplateGenerator] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null)
  const [showBulkOperations, setShowBulkOperations] = useState(false)
  const [bulkSelectMode, setBulkSelectMode] = useState(false)
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const router = useRouter()
  const supabase = useMemo(() => createClient(), [])

  useEffect(() => {
    // Check authentication status
    const checkAuth = async () => {
      try {
        // First check if we have a session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          console.error('Session error:', sessionError)
          router.push('/login')
          return
        }

        if (!session) {
          console.log('No active session, redirecting to login')
          router.push('/login')
          return
        }

        // Then get the user
        const { data: { user }, error: userError } = await supabase.auth.getUser()

        if (userError || !user) {
          console.error('User error:', userError)
          router.push('/login')
          return
        }

        console.log('User authenticated:', user.email)
        setUser(user)
        await loadUserBrands(user.id)
      } catch (error) {
        console.error('Auth check error:', error)
        router.push('/login')
      } finally {
        setLoading(false)
      }
    }

    checkAuth()

    // Initialize current month after hydration to avoid SSR mismatch
    if (!currentMonth) {
      const now = new Date()
      setCurrentMonth(new Date(now.getFullYear(), now.getMonth(), 1))
    }

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth state change:', event, session?.user?.email)

      if (event === 'SIGNED_OUT' || !session) {
        setUser(null)
        setBrands([])
        setCurrentBrand(null)
        setCalendarContent([])
        router.push('/login')
      } else if (session?.user) {
        setUser(session.user)
        loadUserBrands(session.user.id)
      }
    })

    return () => subscription.unsubscribe()
  }, [router, supabase])

  // Save current brand to localStorage
  useEffect(() => {
    if (currentBrand) {
      localStorage.setItem('viralpath-current-brand', JSON.stringify(currentBrand))
    }
  }, [currentBrand])

  // Save calendar content to localStorage
  useEffect(() => {
    if (calendarContent.length > 0) {
      localStorage.setItem('viralpath-calendar-content', JSON.stringify(calendarContent))
    }
  }, [calendarContent])

  // Load calendar content when current month or brand changes
  useEffect(() => {
    if (currentBrand && user) {
      loadCalendarContent(currentBrand.id, user.id)
    }
  }, [currentMonth, currentBrand?.id, user?.id])

  const loadUserBrands = async (userId: string) => {
    try {
      const userBrands = await brandService.getUserBrands(userId)
      setBrands(userBrands)

      // Set the active brand as current
      const activeBrand = userBrands.find(brand => brand.is_active)
      setCurrentBrand(activeBrand || null)

      // Convert and set brand data if active brand exists
      if (activeBrand) {
        const convertedBrandData = convertBrandData(activeBrand)
        setBrandData(convertedBrandData)
        await loadCalendarContent(activeBrand.id, userId)
      } else {
        setBrandData(null)
      }
    } catch (error) {
      console.error('Error loading brands:', error)
    }
  }

  const loadCalendarContent = async (brandId: string, userId: string) => {
    setIsLoadingContent(true)
    setError(null)

    // Check if currentMonth is available, if not use current date
    const monthToUse = currentMonth || new Date()

    const result = await safeAsyncOperation(
      async () => {
        const month = monthToUse.getMonth() + 1
        const year = monthToUse.getFullYear()
        return await contentCalendarService.loadCalendarContent(brandId, userId, month, year)
      },
      {
        context: 'loading calendar content',
        showErrorToast: false,
        fallbackValue: []
      }
    )

    if (result !== null) {
      setCalendarContent(result)
    } else {
      setError('Failed to load calendar content')
    }

    setIsLoadingContent(false)
  }

  const convertBrandData = (brand: Brand): BrandData => {
    return {
      name: brand.name,
      industry: brand.industry,
      tone: brand.tone,
      targetAudience: brand.target_audience,
      platforms: brand.platforms,
      description: brand.description || '',
      website: brand.website || ''
    }
  }

  const handleBrandChange = async (brand: Brand | null) => {
    setCurrentBrand(brand)
    if (brand) {
      // Convert brand data to the format expected by the calendar
      const convertedBrandData = convertBrandData(brand)
      setBrandData(convertedBrandData)

      // Load calendar content for the selected brand
      if (user) {
        await loadCalendarContent(brand.id, user.id)
      }
    } else {
      setBrandData(null)
      setCalendarContent([])
    }
    if (user) {
      loadUserBrands(user.id) // Refresh brands to update active status
    }
  }

  const handleGenerateCalendar = async () => {
    if (!brandData || !currentBrand || !user || !currentMonth) {
      return // This case is now handled by the UI
    }

    setIsGenerating(true)
    setError(null)

    const result = await safeAsyncOperation(
      async () => {
        let content: ContentItem[]

        try {
          // Try to generate content with AI
          content = await generateCalendarContent({
            brandName: brandData.name,
            industry: brandData.industry,
            tone: brandData.tone,
            targetAudience: brandData.targetAudience,
            platforms: brandData.platforms
          }, currentMonth)
        } catch (error) {
          // If rate limited, use demo mode
          if (error instanceof Error && (error.message.includes('Rate limit') || error.message.includes('429'))) {
            console.log('🎭 Switching to demo mode due to rate limiting')
            toast.success('AI service busy - generating high-quality demo content instead!', { duration: 5000 })

            content = await generateDemoCalendarContent({
              brandName: brandData.name,
              industry: brandData.industry,
              tone: brandData.tone,
              targetAudience: brandData.targetAudience,
              platforms: brandData.platforms
            }, currentMonth)
          } else {
            // Re-throw other errors
            throw error
          }
        }

        // Save content to database (whether AI-generated or demo)
        const month = currentMonth.getMonth() + 1
        const year = currentMonth.getFullYear()
        const saved = await contentCalendarService.saveCalendarContent(
          currentBrand.id,
          user.id,
          month,
          year,
          content,
          {
            versionNotes: `Generated ${content.length} posts for ${brandData.platforms.join(', ')}`,
            autoSaved: false
          }
        )

        if (!saved) {
          throw new Error('Failed to save calendar content to database')
        }

        return content
      },
      {
        context: 'generating calendar content',
        retryOptions: { maxRetries: 1, delay: 2000 },
        showErrorToast: false
      }
    )

    if (result) {
      setCalendarContent(result)
      toast.success('Calendar generated and saved successfully!')
    } else {
      setError('Failed to generate calendar content')
    }

    setIsGenerating(false)
  }

  const handleContentClick = (content: ContentItem) => {
    setSelectedContent(content)
    setShowContentModal(true)
  }

  const handleContentUpdate = async (updatedContent: ContentItem, isAutoSave: boolean = false) => {
    if (!currentBrand || !user || !currentMonth) {
      if (!isAutoSave) {
        toast.error('Unable to save changes. Please try again.')
      }
      return
    }

    try {
      const month = currentMonth.getMonth() + 1
      const year = currentMonth.getFullYear()

      const success = await contentCalendarService.updateContentItem(
        currentBrand.id,
        user.id,
        month,
        year,
        updatedContent
      )

      if (success) {
        const updatedCalendar = calendarContent.map(item =>
          item.id === updatedContent.id ? updatedContent : item
        )
        setCalendarContent(updatedCalendar)

        // Only show success toast for manual saves, not auto-saves
        if (!isAutoSave) {
          toast.success('Content updated successfully!')
        }
      } else {
        throw new Error('Failed to update content')
      }
    } catch (error) {
      if (!isAutoSave) {
        toast.error('Failed to update content. Please try again.')
      }
      console.error('Content update error:', error)
    }
  }

  const handleVersionRestore = (content: ContentItem[]) => {
    setCalendarContent(content)
    toast.success('Version restored successfully!')
  }

  const handleTemplateSelect = (template: any) => {
    setSelectedTemplate(template)
    setShowTemplateLibrary(false)
    setShowTemplateGenerator(true)
  }

  const handleTemplateGenerate = (content: {
    caption: string
    hashtags: string[]
    visualPrompt: string
    platform: string
  }) => {
    // Create a new content item from the template
    const newContent: ContentItem = {
      id: Date.now().toString(),
      date: new Date().toISOString().split('T')[0],
      platform: content.platform,
      type: 'post', // Default type
      caption: content.caption,
      hashtags: content.hashtags,
      visualPrompt: content.visualPrompt,
      status: 'draft',
      engagement: { likes: 0, comments: 0, shares: 0 },
      bestTime: '12:00'
    }

    // Add to calendar content
    setCalendarContent(prev => [...prev, newContent])

    // Close template generator and show content modal for editing
    setShowTemplateGenerator(false)
    setSelectedContent(newContent)
    setShowContentModal(true)

    toast.success('Template content generated successfully!')
  }

  const handleBulkOperationsOpen = () => {
    setBulkSelectMode(true)
    setShowBulkOperations(true)
  }

  const handleBulkOperationsClose = () => {
    setBulkSelectMode(false)
    setShowBulkOperations(false)
    setSelectedItems([])
  }

  const handleItemSelect = (itemId: string, selected: boolean) => {
    if (selected) {
      setSelectedItems(prev => [...prev, itemId])
    } else {
      setSelectedItems(prev => prev.filter(id => id !== itemId))
    }
  }

  const handleBulkItemsUpdate = (updatedItems: ContentItem[]) => {
    setCalendarContent(updatedItems)
  }

  const getMonthStats = () => {
    const totalPosts = calendarContent.length
    const platformCounts = brandData?.platforms.reduce((acc, platform) => {
      acc[platform] = calendarContent.filter(item => item.platform === platform).length
      return acc
    }, {} as Record<string, number>) || {}

    return { totalPosts, platformCounts }
  }

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut()
      router.push('/login')
    } catch (error) {
      console.error('Sign out error:', error)
      toast.error('Failed to sign out')
    }
  }

  const stats = getMonthStats()



  if (loading) {
    return (
      <div className="min-h-screen bg-neutral-light flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-purple mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Show brand creation prompt if user has no brands
  if (!loading && brands.length === 0) {
    return (
      <div className="min-h-screen bg-neutral-light flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🚀</div>
          <h2 className="text-2xl font-heading font-bold text-neutral-dark mb-2">
            Welcome to ViralPath.ai!
          </h2>
          <p className="text-gray-600 mb-6">
            Let's create your first brand to start generating amazing content calendars.
          </p>
          <button
            onClick={() => setShowBrandManager(true)}
            className="btn-primary flex items-center gap-2 mx-auto"
          >
            <Plus className="w-4 h-4" />
            Create Your First Brand
          </button>
        </div>

        {/* Include modals even in the welcome screen */}
        <DashboardModals
          showContentModal={showContentModal}
          showExportModal={showExportModal}
          showUserProfile={showUserProfile}
          showBrandManager={showBrandManager}
          showVersionHistory={showVersionHistory}
          isGenerating={isGenerating}
          selectedContent={selectedContent}
          brandData={brandData}
          currentBrand={currentBrand}
          user={user}
          currentMonth={currentMonth}
          calendarContent={calendarContent}
          onContentModalClose={() => setShowContentModal(false)}
          onExportModalClose={() => setShowExportModal(false)}
          onUserProfileClose={() => setShowUserProfile(false)}
          onBrandManagerClose={() => setShowBrandManager(false)}
          onVersionHistoryClose={() => setShowVersionHistory(false)}
          onContentUpdate={handleContentUpdate}
          onVersionRestore={handleVersionRestore}
          onBrandChange={handleBrandChange}
        />

        {/* Posting Time Recommendations for welcome screen */}
        {showPostingTimes && brandData && (
          <PostingTimeRecommendations
            brandData={brandData}
            onClose={() => setShowPostingTimes(false)}
          />
        )}

        {/* Template Library for welcome screen */}
        {showTemplateLibrary && brandData && (
          <TemplateLibrary
            brandData={brandData}
            onTemplateSelect={handleTemplateSelect}
            onClose={() => setShowTemplateLibrary(false)}
          />
        )}

        {/* Template Generator for welcome screen */}
        {showTemplateGenerator && selectedTemplate && brandData && (
          <TemplateGenerator
            template={selectedTemplate}
            brandData={brandData}
            onBack={() => {
              setShowTemplateGenerator(false)
              setShowTemplateLibrary(true)
            }}
            onGenerate={handleTemplateGenerate}
            onClose={() => {
              setShowTemplateGenerator(false)
              setSelectedTemplate(null)
            }}
          />
        )}
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <DashboardHeader
        currentBrand={currentBrand}
        brands={brands}
        currentMonth={currentMonth}
        user={user}
        calendarContent={calendarContent}
        isGenerating={isGenerating}
        onBrandManagerOpen={() => setShowBrandManager(true)}
        onMonthChange={setCurrentMonth}
        onExportOpen={() => setShowExportModal(true)}
        onVersionHistoryOpen={() => setShowVersionHistory(true)}
        onGenerateCalendar={handleGenerateCalendar}
        onSignOut={handleSignOut}
      />

      {/* Main Content */}
      <main className="px-6 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar */}
            <DashboardSidebar
              brandData={brandData}
              stats={stats}
              isLoadingContent={isLoadingContent}
              onBrandManagerOpen={() => setShowBrandManager(true)}
              onPostingTimesOpen={() => setShowPostingTimes(true)}
              onTemplateLibraryOpen={() => setShowTemplateLibrary(true)}
              onBulkOperationsOpen={handleBulkOperationsOpen}
            />

            {/* Main Content Area */}
            <DashboardMainContent
              currentBrand={currentBrand ? adaptDatabaseBrandToBrand(currentBrand) : null}
              calendarContent={calendarContent}
              currentMonth={currentMonth}
              isLoadingContent={isLoadingContent}
              isGenerating={isGenerating}
              error={error}
              onBrandManagerOpen={() => setShowBrandManager(true)}
              onContentClick={handleContentClick}
              onGenerateCalendar={handleGenerateCalendar}
              onRetryLoad={() => currentBrand && user && loadCalendarContent(currentBrand.id, user.id)}
              bulkSelectMode={bulkSelectMode}
              selectedItems={selectedItems}
              onItemSelect={handleItemSelect}
            />

          </div>
        </div>
      </main>

      {/* Modals */}
      <DashboardModals
        showContentModal={showContentModal}
        showExportModal={showExportModal}
        showUserProfile={showUserProfile}
        showBrandManager={showBrandManager}
        showVersionHistory={showVersionHistory}
        isGenerating={isGenerating}
        selectedContent={selectedContent}
        brandData={brandData}
        currentBrand={currentBrand}
        user={user}
        currentMonth={currentMonth}
        calendarContent={calendarContent}
        onContentModalClose={() => setShowContentModal(false)}
        onExportModalClose={() => setShowExportModal(false)}
        onUserProfileClose={() => setShowUserProfile(false)}
        onBrandManagerClose={() => setShowBrandManager(false)}
        onVersionHistoryClose={() => setShowVersionHistory(false)}
        onContentUpdate={handleContentUpdate}
        onVersionRestore={handleVersionRestore}
        onBrandChange={handleBrandChange}
      />

      {/* Posting Time Recommendations */}
      {showPostingTimes && brandData && (
        <PostingTimeRecommendations
          brandData={brandData}
          onClose={() => setShowPostingTimes(false)}
        />
      )}

      {/* Template Library */}
      {showTemplateLibrary && brandData && (
        <TemplateLibrary
          brandData={brandData}
          onTemplateSelect={handleTemplateSelect}
          onClose={() => setShowTemplateLibrary(false)}
        />
      )}

      {/* Template Generator */}
      {showTemplateGenerator && selectedTemplate && brandData && (
        <TemplateGenerator
          template={selectedTemplate}
          brandData={brandData}
          onBack={() => {
            setShowTemplateGenerator(false)
            setShowTemplateLibrary(true)
          }}
          onGenerate={handleTemplateGenerate}
          onClose={() => {
            setShowTemplateGenerator(false)
            setSelectedTemplate(null)
          }}
        />
      )}

      {/* Bulk Operations */}
      {showBulkOperations && (
        <BulkOperations
          items={calendarContent}
          selectedItems={selectedItems}
          onSelectionChange={setSelectedItems}
          onItemsUpdate={handleBulkItemsUpdate}
          onClose={handleBulkOperationsClose}
          brandData={brandData}
        />
      )}
    </div>
  )
}