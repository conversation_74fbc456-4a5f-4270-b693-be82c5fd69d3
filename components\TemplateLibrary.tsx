'use client'

import { useState, useEffect } from 'react'
import { Search, Filter, Star, Users, TrendingUp, BookOpen, Megaphone, Heart, Camera, X, Play, Eye } from 'lucide-react'
import { ContentTemplateService, ContentTemplate } from '@/lib/services/contentTemplateService'

interface TemplateLibraryProps {
  brandData: {
    industry: string
    platforms: string[]
  }
  onTemplateSelect: (template: ContentTemplate) => void
  onClose: () => void
}

const categoryIcons = {
  educational: BookOpen,
  promotional: Megaphone,
  entertainment: Play,
  inspirational: Heart,
  'behind-the-scenes': Camera,
  'user-generated': Users
}

const difficultyColors = {
  beginner: 'bg-green-100 text-green-800',
  intermediate: 'bg-yellow-100 text-yellow-800',
  advanced: 'bg-red-100 text-red-800'
}

const engagementColors = {
  low: 'text-gray-600',
  medium: 'text-yellow-600',
  high: 'text-green-600'
}

export default function TemplateLibrary({
  brandData,
  onTemplateSelect,
  onClose
}: TemplateLibraryProps) {
  const [templates, setTemplates] = useState<ContentTemplate[]>([])
  const [filteredTemplates, setFilteredTemplates] = useState<ContentTemplate[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all')
  const [selectedPlatform, setSelectedPlatform] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [previewTemplate, setPreviewTemplate] = useState<ContentTemplate | null>(null)

  const categories = ContentTemplateService.getCategories()
  const industries = ContentTemplateService.getIndustries()

  useEffect(() => {
    // Load all templates
    const allTemplates = ContentTemplateService.getAllTemplates()
    setTemplates(allTemplates)
    setFilteredTemplates(allTemplates)
  }, [])

  useEffect(() => {
    // Apply filters
    const filters = {
      category: selectedCategory !== 'all' ? selectedCategory : undefined,
      industry: brandData.industry,
      platform: selectedPlatform !== 'all' ? selectedPlatform : undefined,
      difficulty: selectedDifficulty !== 'all' ? selectedDifficulty : undefined,
      search: searchQuery || undefined
    }

    const filtered = ContentTemplateService.getTemplates(filters)
    setFilteredTemplates(filtered)
  }, [selectedCategory, selectedDifficulty, selectedPlatform, searchQuery, brandData.industry])

  const handleTemplateSelect = (template: ContentTemplate) => {
    ContentTemplateService.incrementUsage(template.id)
    onTemplateSelect(template)
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-3 h-3 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ))
  }

  const getEngagementIcon = (level: string) => {
    switch (level) {
      case 'high': return <TrendingUp className="w-4 h-4 text-green-600" />
      case 'medium': return <TrendingUp className="w-4 h-4 text-yellow-600" />
      default: return <TrendingUp className="w-4 h-4 text-gray-600" />
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-7xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-neutral-dark flex items-center gap-2">
              <BookOpen className="w-5 h-5 text-primary-purple" />
              Content Template Library
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Choose from {filteredTemplates.length} professional templates for {brandData.industry}
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-150"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Search and Filters */}
        <div className="p-6 border-b border-gray-100 bg-gray-50">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-purple focus:border-transparent"
              />
            </div>

            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Filter className="w-4 h-4" />
              Filters
            </button>
          </div>

          {/* Filter Options */}
          {showFilters && (
            <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-purple focus:border-transparent"
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ')}
                    </option>
                  ))}
                </select>
              </div>

              {/* Platform Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Platform</label>
                <select
                  value={selectedPlatform}
                  onChange={(e) => setSelectedPlatform(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-purple focus:border-transparent"
                >
                  <option value="all">All Platforms</option>
                  {brandData.platforms.map(platform => (
                    <option key={platform} value={platform}>
                      {platform.charAt(0).toUpperCase() + platform.slice(1)}
                    </option>
                  ))}
                </select>
              </div>

              {/* Difficulty Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Difficulty</label>
                <select
                  value={selectedDifficulty}
                  onChange={(e) => setSelectedDifficulty(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-purple focus:border-transparent"
                >
                  <option value="all">All Levels</option>
                  <option value="beginner">Beginner</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                </select>
              </div>
            </div>
          )}
        </div>

        {/* Templates Grid */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {filteredTemplates.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTemplates.map((template) => {
                const CategoryIcon = categoryIcons[template.category] || BookOpen
                
                return (
                  <div
                    key={template.id}
                    className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all duration-200 cursor-pointer group"
                  >
                    {/* Template Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-primary-purple/10 rounded-lg flex items-center justify-center">
                          <CategoryIcon className="w-5 h-5 text-primary-purple" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-neutral-dark group-hover:text-primary-purple transition-colors">
                            {template.name}
                          </h3>
                          <div className="flex items-center gap-1 mt-1">
                            {renderStars(template.rating)}
                            <span className="text-xs text-gray-500 ml-1">
                              ({template.usageCount})
                            </span>
                          </div>
                        </div>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          setPreviewTemplate(template)
                        }}
                        className="p-1 hover:bg-gray-100 rounded transition-colors"
                      >
                        <Eye className="w-4 h-4 text-gray-400" />
                      </button>
                    </div>

                    {/* Template Description */}
                    <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                      {template.description}
                    </p>

                    {/* Template Metadata */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${difficultyColors[template.difficulty]}`}>
                          {template.difficulty}
                        </span>
                        <div className="flex items-center gap-1">
                          {getEngagementIcon(template.estimatedEngagement)}
                          <span className={`text-xs ${engagementColors[template.estimatedEngagement]}`}>
                            {template.estimatedEngagement}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Platforms */}
                    <div className="flex flex-wrap gap-1 mb-4">
                      {template.platforms.slice(0, 3).map(platform => (
                        <span
                          key={platform}
                          className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                        >
                          {platform}
                        </span>
                      ))}
                      {template.platforms.length > 3 && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                          +{template.platforms.length - 3}
                        </span>
                      )}
                    </div>

                    {/* Action Button */}
                    <button
                      onClick={() => handleTemplateSelect(template)}
                      className="w-full bg-gradient-primary text-white py-2 rounded-lg hover:shadow-lg transition-all duration-200 text-sm font-medium"
                    >
                      Use Template
                    </button>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <BookOpen className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
              <p className="text-gray-600 mb-4">
                Try adjusting your filters or search terms
              </p>
              <button
                onClick={() => {
                  setSearchQuery('')
                  setSelectedCategory('all')
                  setSelectedDifficulty('all')
                  setSelectedPlatform('all')
                }}
                className="text-primary-purple hover:text-primary-purple/80 font-medium"
              >
                Clear all filters
              </button>
            </div>
          )}
        </div>

        {/* Template Preview Modal */}
        {previewTemplate && (
          <div className="fixed inset-0 z-60 flex items-center justify-center bg-black/50 backdrop-blur-sm">
            <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden">
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h3 className="font-semibold text-neutral-dark">{previewTemplate.name}</h3>
                <button
                  onClick={() => setPreviewTemplate(null)}
                  className="p-1 hover:bg-gray-100 rounded transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              <div className="p-4 overflow-y-auto max-h-[calc(80vh-80px)]">
                <p className="text-gray-600 mb-4">{previewTemplate.description}</p>
                
                {previewTemplate.examples.length > 0 && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">Example Preview:</h4>
                    <div className="bg-white rounded-lg p-3 border">
                      <p className="text-sm whitespace-pre-line mb-2">
                        {previewTemplate.examples[0].preview.caption}
                      </p>
                      <div className="flex flex-wrap gap-1">
                        {previewTemplate.examples[0].preview.hashtags.map((tag, index) => (
                          <span key={index} className="text-xs text-blue-600">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
                
                <div className="mt-4 flex gap-2">
                  <button
                    onClick={() => {
                      handleTemplateSelect(previewTemplate)
                      setPreviewTemplate(null)
                    }}
                    className="flex-1 bg-gradient-primary text-white py-2 rounded-lg hover:shadow-lg transition-all duration-200 text-sm font-medium"
                  >
                    Use This Template
                  </button>
                  <button
                    onClick={() => setPreviewTemplate(null)}
                    className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
