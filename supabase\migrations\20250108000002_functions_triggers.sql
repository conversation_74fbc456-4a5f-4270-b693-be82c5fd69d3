-- Database Functions and Triggers for ViralPath.ai
-- Automates common tasks and maintains data integrity

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, full_name, avatar_url)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name'),
        NEW.raw_user_meta_data->>'avatar_url'
    );
    
    -- Create default subscription (free tier)
    INSERT INTO public.subscriptions (user_id, status, tier, current_period_start, current_period_end, trial_end)
    VALUES (
        NEW.id,
        'trialing',
        'free',
        NOW(),
        NOW() + INTERVAL '30 days',
        NOW() + INTERVAL '14 days'
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log audit events
CREATE OR REPLACE FUNCTION public.log_audit_event(
    p_user_id UUID,
    p_action audit_action,
    p_resource_type TEXT,
    p_resource_id UUID DEFAULT NULL,
    p_old_values JSONB DEFAULT NULL,
    p_new_values JSONB DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    audit_id UUID;
BEGIN
    INSERT INTO public.audit_logs (
        user_id,
        action,
        resource_type,
        resource_id,
        old_values,
        new_values,
        metadata
    ) VALUES (
        p_user_id,
        p_action,
        p_resource_type,
        p_resource_id,
        p_old_values,
        p_new_values,
        p_metadata
    ) RETURNING id INTO audit_id;
    
    RETURN audit_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to track usage
CREATE OR REPLACE FUNCTION public.track_usage(
    p_user_id UUID,
    p_resource_type TEXT,
    p_amount INTEGER DEFAULT 1,
    p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    usage_id UUID;
    subscription_id UUID;
BEGIN
    -- Get user's current subscription
    SELECT id INTO subscription_id
    FROM public.subscriptions
    WHERE user_id = p_user_id AND status = 'active'
    ORDER BY created_at DESC
    LIMIT 1;
    
    INSERT INTO public.usage_tracking (
        user_id,
        subscription_id,
        resource_type,
        amount,
        metadata
    ) VALUES (
        p_user_id,
        subscription_id,
        p_resource_type,
        p_amount,
        p_metadata
    ) RETURNING id INTO usage_id;
    
    RETURN usage_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check usage limits
CREATE OR REPLACE FUNCTION public.check_usage_limit(
    p_user_id UUID,
    p_resource_type TEXT,
    p_period_days INTEGER DEFAULT 30
)
RETURNS JSONB AS $$
DECLARE
    current_usage INTEGER;
    usage_limit INTEGER;
    subscription_tier subscription_tier;
    result JSONB;
BEGIN
    -- Get user's subscription tier
    SELECT tier INTO subscription_tier
    FROM public.subscriptions
    WHERE user_id = p_user_id AND status IN ('active', 'trialing')
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- Set limits based on tier and resource type
    CASE subscription_tier
        WHEN 'free' THEN
            CASE p_resource_type
                WHEN 'content_generation' THEN usage_limit := 50;
                WHEN 'api_calls' THEN usage_limit := 100;
                WHEN 'storage' THEN usage_limit := 1000; -- MB
                ELSE usage_limit := 10;
            END CASE;
        WHEN 'starter' THEN
            CASE p_resource_type
                WHEN 'content_generation' THEN usage_limit := 500;
                WHEN 'api_calls' THEN usage_limit := 1000;
                WHEN 'storage' THEN usage_limit := 10000; -- MB
                ELSE usage_limit := 100;
            END CASE;
        WHEN 'professional' THEN
            CASE p_resource_type
                WHEN 'content_generation' THEN usage_limit := 2000;
                WHEN 'api_calls' THEN usage_limit := 5000;
                WHEN 'storage' THEN usage_limit := 50000; -- MB
                ELSE usage_limit := 500;
            END CASE;
        WHEN 'enterprise' THEN
            usage_limit := -1; -- Unlimited
        ELSE
            usage_limit := 0;
    END CASE;
    
    -- Get current usage for the period
    SELECT COALESCE(SUM(amount), 0) INTO current_usage
    FROM public.usage_tracking
    WHERE user_id = p_user_id
        AND resource_type = p_resource_type
        AND created_at >= NOW() - INTERVAL '1 day' * p_period_days;
    
    result := jsonb_build_object(
        'current_usage', current_usage,
        'usage_limit', usage_limit,
        'remaining', CASE WHEN usage_limit = -1 THEN -1 ELSE GREATEST(0, usage_limit - current_usage) END,
        'percentage_used', CASE WHEN usage_limit = -1 THEN 0 ELSE LEAST(100, (current_usage::FLOAT / usage_limit * 100)::INTEGER) END,
        'is_over_limit', CASE WHEN usage_limit = -1 THEN false ELSE current_usage > usage_limit END
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create notification
CREATE OR REPLACE FUNCTION public.create_notification(
    p_user_id UUID,
    p_type notification_type,
    p_title TEXT,
    p_message TEXT,
    p_action_url TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO public.notifications (
        user_id,
        type,
        title,
        message,
        action_url,
        metadata
    ) VALUES (
        p_user_id,
        p_type,
        p_title,
        p_message,
        p_action_url,
        p_metadata
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's feature flags
CREATE OR REPLACE FUNCTION public.get_user_feature_flags(p_user_id UUID)
RETURNS TABLE(name TEXT, is_enabled BOOLEAN) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ff.name,
        ff.is_enabled
    FROM public.feature_flags ff
    LEFT JOIN public.subscriptions s ON s.user_id = p_user_id
    WHERE ff.is_enabled = true
        AND (
            ff.rollout_percentage = 100
            OR p_user_id = ANY(ff.target_users)
            OR s.tier = ANY(ff.target_tiers)
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for updated_at columns
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON public.subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_brands_updated_at BEFORE UPDATE ON public.brands
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_calendars_updated_at BEFORE UPDATE ON public.content_calendars
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_posts_updated_at BEFORE UPDATE ON public.content_posts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_templates_updated_at BEFORE UPDATE ON public.content_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_webhooks_updated_at BEFORE UPDATE ON public.webhooks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_feature_flags_updated_at BEFORE UPDATE ON public.feature_flags
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for new user signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create audit triggers for important tables
CREATE OR REPLACE FUNCTION public.audit_brands_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM public.log_audit_event(
            NEW.user_id,
            'create',
            'brand',
            NEW.id,
            NULL,
            to_jsonb(NEW),
            '{}'
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        PERFORM public.log_audit_event(
            NEW.user_id,
            'update',
            'brand',
            NEW.id,
            to_jsonb(OLD),
            to_jsonb(NEW),
            '{}'
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM public.log_audit_event(
            OLD.user_id,
            'delete',
            'brand',
            OLD.id,
            to_jsonb(OLD),
            NULL,
            '{}'
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER audit_brands_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.brands
    FOR EACH ROW EXECUTE FUNCTION public.audit_brands_changes();
