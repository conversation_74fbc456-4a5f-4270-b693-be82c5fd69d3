# ViralPath.ai 🚀

AI-powered social media calendar generation for marketers, creators, and agencies. Create viral content that drives engagement across all platforms.

## ✨ Features

- **AI-Powered Content Generation**: Generate 30-day content calendars with captions, hashtags, and visual prompts
- **Multi-Platform Support**: Instagram, TikTok, LinkedIn, and X/Twitter optimization
- **Smart Timing**: AI recommendations for optimal posting times
- **Brand Consistency**: Maintain your unique voice and tone across all content
- **Export Options**: CSV, JSON, and Calendar file formats
- **Responsive Design**: Beautiful UI that works on all devices

## 🛠️ Tech Stack

- **Frontend**: Next.js 15 + TypeScript + Tailwind CSS
- **AI Engine**: OpenRouter + Google Gemini with multiple model options (GPT-4, Claude, Gemini, etc.)
- **Database**: Supabase (PostgreSQL + Auth + Storage)
- **Hosting**: Vercel (frontend) + Supabase (backend)
- **Styling**: Tailwind CSS with custom design system

## ✨ Key Features

### 🤖 **AI Model Selection**
- **Latest AI Models**: Choose from GPT-4, Claude 3, Gemini 2.0 Flash, DeepSeek, and more
- **Gemini 2.0 Flash**: Latest Google AI model with enhanced speed and capabilities
- **Dual API Support**: OpenRouter and Google AI APIs integrated seamlessly
- **Free & Premium Options**: Start with free models, upgrade for better quality
- **Real-time Switching**: Change models anytime from the dashboard
- **Cost Transparency**: Clear pricing for each model tier

### 📅 **Smart Content Calendar**
- **30-Day Generation**: Complete monthly content calendars in seconds
- **Multi-Platform**: Instagram, TikTok, LinkedIn, Twitter support
- **Platform Optimization**: Content tailored for each platform's best practices
- **Bulk Operations**: Edit, regenerate, and export multiple posts at once

### 🔥 **Viral Content Generation**
- **Advanced Prompt Engineering**: Sophisticated framework for viral content creation
- **Psychology-Driven**: Content designed using proven viral psychology triggers
- **Engagement Hooks**: Built-in elements that drive likes, shares, and comments
- **Viral Score Prediction**: AI predicts viral potential of each piece of content
- **Platform-Specific Optimization**: Tailored for each social media platform's algorithm

### 🎯 **Advanced Features**
- **Template Library**: Pre-built content templates for various industries
- **Posting Time Optimization**: AI-recommended optimal posting times
- **Brand Voice Consistency**: Maintain consistent tone across all content
- **Rate Limit Handling**: Graceful fallback to demo content when needed

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ installed
- OpenRouter API key (for AI content generation)
- Supabase account (optional for full functionality)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd viral-path
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```
   
   Edit `.env.local` and add your API keys:
   ```env
   # OpenRouter API key is hardcoded - users can select from multiple AI models in the dashboard
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📱 Usage

### Getting Started

1. **Brand Setup**: Complete the onboarding flow to set up your brand profile
2. **Platform Selection**: Choose which social media platforms you want to target
3. **Generate Calendar**: Let AI create your 30-day content calendar
4. **Edit & Customize**: Fine-tune individual posts to match your vision
5. **Export & Schedule**: Export to your preferred format or scheduling tool

### Key Features

#### 🎯 Brand Onboarding
- Define your brand voice and tone
- Select target audience
- Choose industry and content themes

#### 🤖 AI Content Generation
- Platform-optimized content
- Trending hashtags and keywords
- Visual content prompts
- Optimal posting times

#### 📊 Calendar Management
- Interactive monthly view
- Drag-and-drop editing
- Content preview and editing
- Performance insights

#### 📤 Export Options
- **CSV**: For spreadsheet applications
- **JSON**: For developers and integrations
- **ICS**: For calendar applications

## 🎨 Design System

### Color Palette
- **Primary Purple**: `#5E2CED`
- **Primary Blue**: `#1FB6FF`
- **Neutral Dark**: `#2E3148`
- **Neutral Light**: `#F5F7FA`

### Typography
- **Primary**: Inter (body text)
- **Headings**: Poppins (headings and emphasis)

### Components
- Gradient buttons and accents
- Soft shadows and rounded corners
- Responsive grid system
- Smooth animations and transitions

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|---------|
| `NEXT_PUBLIC_SUPABASE_URL` | Supabase project URL | Optional |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Supabase anonymous key | Optional |
| `NEXT_PUBLIC_APP_URL` | Application URL | No |

**Note**: OpenRouter API key is hardcoded in the service. Users can select from multiple AI models including free and premium options.

### Platform Configuration

Each platform has specific settings for:
- Content types (photo, video, article, etc.)
- Optimal posting times
- Hashtag recommendations
- Character limits

## 📦 Project Structure

```
viralpath-ai/
├── app/                    # Next.js app directory
│   ├── dashboard/         # Dashboard pages
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # Reusable components
│   ├── CalendarGrid.tsx   # Calendar display
│   ├── ContentModal.tsx   # Content editing
│   ├── ExportModal.tsx    # Export functionality
│   ├── Header.tsx         # Navigation
│   ├── Footer.tsx         # Footer
│   └── OnboardingModal.tsx # Brand setup
├── lib/                   # Utility functions
│   └── ai-service.ts      # AI content generation
├── public/                # Static assets
└── package.json           # Dependencies
```

## 🚀 Deployment

### Vercel (Recommended)

1. **Connect your repository** to Vercel
2. **Set environment variables** in Vercel dashboard
3. **Deploy** - Vercel will automatically build and deploy

### Manual Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Start the production server**
   ```bash
   npm start
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

If you encounter any issues or have questions:

1. Check the [Issues](../../issues) page
2. Create a new issue with detailed information
3. Contact <NAME_EMAIL>

## 🎯 Roadmap

- [ ] Analytics dashboard
- [ ] Team collaboration features
- [ ] Multi-language support
- [ ] Template library
- [ ] Direct social media scheduling
- [ ] Performance tracking
- [ ] A/B testing for content

---

**Made with ❤️ for content creators worldwide**

ViralPath.ai - Transform your social media strategy with AI-powered content generation.