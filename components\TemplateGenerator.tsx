'use client'

import { useState, useEffect } from 'react'
import { Arrow<PERSON>eft, Wand2, Co<PERSON>, Download, RefreshCw, Eye, X, Sparkles } from 'lucide-react'
import { ContentTemplateService, ContentTemplate } from '@/lib/services/contentTemplateService'
import { toast } from 'react-hot-toast'

interface TemplateGeneratorProps {
  template: ContentTemplate
  brandData: {
    platforms: string[]
  }
  onBack: () => void
  onGenerate: (content: {
    caption: string
    hashtags: string[]
    visualPrompt: string
    platform: string
  }) => void
  onClose: () => void
}

export default function TemplateGenerator({
  template,
  brandData,
  onBack,
  onGenerate,
  onClose
}: TemplateGeneratorProps) {
  const [variables, setVariables] = useState<Record<string, any>>({})
  const [selectedPlatform, setSelectedPlatform] = useState<string>(
    brandData.platforms.find(p => template.platforms.includes(p)) || brandData.platforms[0]
  )
  const [generatedContent, setGeneratedContent] = useState<{
    caption: string
    hashtags: string[]
    visualPrompt: string
  } | null>(null)
  const [showPreview, setShowPreview] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [variations, setVariations] = useState<Array<{
    id: string
    name: string
    content: {
      caption: string
      hashtags: string[]
      visualPrompt: string
    }
  }>>([])

  // Initialize variables with default values
  useEffect(() => {
    const initialVariables: Record<string, any> = {}
    Object.entries(template.variables).forEach(([key, config]) => {
      if (config.type === 'select' && config.options) {
        initialVariables[key] = config.options[0]
      } else {
        initialVariables[key] = ''
      }
    })
    setVariables(initialVariables)
  }, [template])

  const handleVariableChange = (key: string, value: any) => {
    setVariables(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const generateContent = () => {
    setIsGenerating(true)
    
    // Simulate AI processing time
    setTimeout(() => {
      const content = ContentTemplateService.generateFromTemplate(
        template.id,
        variables,
        selectedPlatform
      )
      
      if (content) {
        setGeneratedContent(content)
        setShowPreview(true)
      } else {
        toast.error('Failed to generate content. Please check your inputs.')
      }
      
      setIsGenerating(false)
    }, 1500)
  }

  const generateVariations = () => {
    if (!generatedContent) return

    const newVariations = []
    
    // Generate 3 variations with slight modifications
    for (let i = 1; i <= 3; i++) {
      const variationVariables = { ...variables }
      
      // Add some variation to the content
      if (template.category === 'educational') {
        const tips = ['Quick tip', 'Pro tip', 'Expert advice', 'Insider secret']
        variationVariables.protip = tips[Math.floor(Math.random() * tips.length)] + ': ' + variables.protip
      }
      
      const content = ContentTemplateService.generateFromTemplate(
        template.id,
        variationVariables,
        selectedPlatform
      )
      
      if (content) {
        newVariations.push({
          id: `var_${i}`,
          name: `Variation ${i}`,
          content
        })
      }
    }
    
    setVariations(newVariations)
    toast.success(`Generated ${newVariations.length} variations!`)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard!')
  }

  const handleUseContent = (content: {
    caption: string
    hashtags: string[]
    visualPrompt: string
  }) => {
    onGenerate({
      ...content,
      platform: selectedPlatform
    })
  }

  const isFormValid = () => {
    return Object.entries(template.variables).every(([key, config]) => {
      if (config.required) {
        return variables[key] && variables[key].toString().trim() !== ''
      }
      return true
    })
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-4">
            <button
              onClick={onBack}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-150"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <h2 className="text-xl font-semibold text-neutral-dark flex items-center gap-2">
                <Wand2 className="w-5 h-5 text-primary-purple" />
                {template.name}
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                Customize your template and generate content
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-150"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex h-[calc(90vh-80px)]">
          {/* Left Panel - Form */}
          <div className="w-1/2 p-6 border-r border-gray-200 overflow-y-auto">
            <div className="space-y-6">
              {/* Platform Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Target Platform
                </label>
                <div className="flex gap-2">
                  {brandData.platforms.filter(p => template.platforms.includes(p)).map(platform => (
                    <button
                      key={platform}
                      onClick={() => setSelectedPlatform(platform)}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors capitalize ${
                        selectedPlatform === platform
                          ? 'bg-primary-purple text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {platform}
                    </button>
                  ))}
                </div>
              </div>

              {/* Template Variables */}
              <div className="space-y-4">
                <h3 className="font-medium text-gray-900">Template Variables</h3>
                {Object.entries(template.variables).map(([key, config]) => (
                  <div key={key}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {config.label}
                      {config.required && <span className="text-red-500 ml-1">*</span>}
                    </label>
                    
                    {config.type === 'text' ? (
                      <input
                        type="text"
                        value={variables[key] || ''}
                        onChange={(e) => handleVariableChange(key, e.target.value)}
                        placeholder={config.placeholder}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-purple focus:border-transparent"
                      />
                    ) : config.type === 'select' ? (
                      <select
                        value={variables[key] || ''}
                        onChange={(e) => handleVariableChange(key, e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-purple focus:border-transparent"
                      >
                        {config.options?.map(option => (
                          <option key={option} value={option}>
                            {option}
                          </option>
                        ))}
                      </select>
                    ) : config.type === 'number' ? (
                      <input
                        type="number"
                        value={variables[key] || ''}
                        onChange={(e) => handleVariableChange(key, parseInt(e.target.value))}
                        placeholder={config.placeholder}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-purple focus:border-transparent"
                      />
                    ) : null}
                  </div>
                ))}
              </div>

              {/* Generate Button */}
              <button
                onClick={generateContent}
                disabled={!isFormValid() || isGenerating}
                className="w-full bg-gradient-primary text-white py-3 rounded-lg hover:shadow-lg transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              >
                {isGenerating ? (
                  <>
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles className="w-4 h-4" />
                    Generate Content
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Right Panel - Preview */}
          <div className="w-1/2 p-6 overflow-y-auto bg-gray-50">
            {generatedContent ? (
              <div className="space-y-6">
                {/* Generated Content */}
                <div className="bg-white rounded-xl p-6 shadow-sm">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-medium text-gray-900">Generated Content</h3>
                    <div className="flex gap-2">
                      <button
                        onClick={() => copyToClipboard(generatedContent.caption)}
                        className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                        title="Copy caption"
                      >
                        <Copy className="w-4 h-4 text-gray-600" />
                      </button>
                      <button
                        onClick={() => setShowPreview(true)}
                        className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                        title="Full preview"
                      >
                        <Eye className="w-4 h-4 text-gray-600" />
                      </button>
                    </div>
                  </div>

                  {/* Caption */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Caption</label>
                    <div className="bg-gray-50 rounded-lg p-3 text-sm whitespace-pre-line">
                      {generatedContent.caption}
                    </div>
                  </div>

                  {/* Hashtags */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Hashtags</label>
                    <div className="flex flex-wrap gap-1">
                      {generatedContent.hashtags.map((tag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Visual Prompt */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Visual Prompt</label>
                    <div className="bg-gray-50 rounded-lg p-3 text-sm">
                      {generatedContent.visualPrompt}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-3">
                    <button
                      onClick={() => handleUseContent(generatedContent)}
                      className="flex-1 bg-gradient-primary text-white py-2 rounded-lg hover:shadow-lg transition-all duration-200 text-sm font-medium"
                    >
                      Use This Content
                    </button>
                    <button
                      onClick={generateVariations}
                      className="px-4 py-2 border border-primary-purple text-primary-purple rounded-lg hover:bg-primary-purple hover:text-white transition-colors text-sm font-medium"
                    >
                      Generate Variations
                    </button>
                  </div>
                </div>

                {/* Variations */}
                {variations.length > 0 && (
                  <div className="space-y-4">
                    <h3 className="font-medium text-gray-900">Content Variations</h3>
                    {variations.map((variation) => (
                      <div key={variation.id} className="bg-white rounded-lg p-4 shadow-sm">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium text-sm text-gray-900">{variation.name}</h4>
                          <button
                            onClick={() => handleUseContent(variation.content)}
                            className="px-3 py-1 bg-primary-purple text-white text-xs rounded-lg hover:shadow-lg transition-all duration-200"
                          >
                            Use This
                          </button>
                        </div>
                        <p className="text-sm text-gray-600 line-clamp-3">
                          {variation.content.caption}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <Wand2 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Generate</h3>
                  <p className="text-gray-600">
                    Fill in the template variables and click "Generate Content" to see your customized content.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Full Preview Modal */}
        {showPreview && generatedContent && (
          <div className="fixed inset-0 z-60 flex items-center justify-center bg-black/50 backdrop-blur-sm">
            <div className="bg-white rounded-xl shadow-2xl w-full max-w-lg mx-4 max-h-[80vh] overflow-hidden">
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h3 className="font-semibold text-neutral-dark">Content Preview</h3>
                <button
                  onClick={() => setShowPreview(false)}
                  className="p-1 hover:bg-gray-100 rounded transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              <div className="p-6">
                <div className="bg-gray-50 rounded-lg p-4 mb-4">
                  <p className="text-sm whitespace-pre-line mb-3">
                    {generatedContent.caption}
                  </p>
                  <div className="flex flex-wrap gap-1 mb-3">
                    {generatedContent.hashtags.map((tag, index) => (
                      <span key={index} className="text-xs text-blue-600">
                        {tag}
                      </span>
                    ))}
                  </div>
                  <div className="text-xs text-gray-500 border-t pt-2">
                    Visual: {generatedContent.visualPrompt}
                  </div>
                </div>
                <button
                  onClick={() => {
                    handleUseContent(generatedContent)
                    setShowPreview(false)
                  }}
                  className="w-full bg-gradient-primary text-white py-2 rounded-lg hover:shadow-lg transition-all duration-200 text-sm font-medium"
                >
                  Use This Content
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
