-- Create brands table
CREATE TABLE IF NOT EXISTS public.brands (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  industry TEXT NOT NULL,
  tone TEXT NOT NULL,
  target_audience TEXT NOT NULL,
  platforms TEXT[] NOT NULL DEFAULT '{}',
  description TEXT,
  website TEXT,
  is_active BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create content_calendars table
CREATE TABLE IF NOT EXISTS public.content_calendars (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  brand_id UUID NOT NULL REFERENCES public.brands(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  month INTEGER NOT NULL,
  year INTEGER NOT NULL,
  content JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(brand_id, month, year)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_brands_user_id ON public.brands(user_id);
CREATE INDEX IF NOT EXISTS idx_brands_user_active ON public.brands(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_content_calendars_brand_id ON public.content_calendars(brand_id);
CREATE INDEX IF NOT EXISTS idx_content_calendars_user_id ON public.content_calendars(user_id);
CREATE INDEX IF NOT EXISTS idx_content_calendars_date ON public.content_calendars(year, month);

-- Enable Row Level Security (RLS)
ALTER TABLE public.brands ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_calendars ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for brands table
CREATE POLICY "Users can view their own brands" ON public.brands
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own brands" ON public.brands
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own brands" ON public.brands
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own brands" ON public.brands
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for content_calendars table
CREATE POLICY "Users can view their own content calendars" ON public.content_calendars
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own content calendars" ON public.content_calendars
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own content calendars" ON public.content_calendars
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own content calendars" ON public.content_calendars
  FOR DELETE USING (auth.uid() = user_id);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = timezone('utc'::text, now());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update updated_at
CREATE TRIGGER handle_brands_updated_at
  BEFORE UPDATE ON public.brands
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_content_calendars_updated_at
  BEFORE UPDATE ON public.content_calendars
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();

-- Create function to ensure only one active brand per user
CREATE OR REPLACE FUNCTION public.ensure_single_active_brand()
RETURNS TRIGGER AS $$
BEGIN
  -- If setting a brand as active, deactivate all other brands for this user
  IF NEW.is_active = true THEN
    UPDATE public.brands 
    SET is_active = false 
    WHERE user_id = NEW.user_id AND id != NEW.id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to ensure only one active brand per user
CREATE TRIGGER ensure_single_active_brand_trigger
  BEFORE INSERT OR UPDATE ON public.brands
  FOR EACH ROW
  EXECUTE FUNCTION public.ensure_single_active_brand();