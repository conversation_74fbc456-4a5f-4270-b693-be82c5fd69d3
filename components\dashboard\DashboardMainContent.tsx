'use client'

import { Calendar, Building2, Plus, RefreshCw } from 'lucide-react'
import CalendarGrid from '@/components/CalendarGrid'
import { CalendarGridSkeleton } from '@/components/SkeletonLoader'
import { LoadingError, GenerationError, NetworkError } from '@/components/ErrorMessage'
import { ContentItem, Brand } from '@/lib/types/content'

interface DashboardMainContentProps {
  currentBrand: Brand | null
  calendarContent: ContentItem[]
  currentMonth: Date | null
  isLoadingContent: boolean
  isGenerating: boolean
  error: string | null
  onBrandManagerOpen: () => void
  onContentClick: (content: ContentItem) => void
  onGenerateCalendar: () => void
  onRetryLoad: () => void
  bulkSelectMode?: boolean
  selectedItems?: string[]
  onItemSelect?: (itemId: string, selected: boolean) => void
}

export default function DashboardMainContent({
  currentBrand,
  calendarContent,
  currentMonth,
  isLoadingContent,
  isGenerating,
  error,
  onBrandManagerOpen,
  onContentClick,
  onGenerateCalendar,
  onRetryLoad,
  bulkSelectMode = false,
  selectedItems = [],
  onItemSelect
}: DashboardMainContentProps) {
  // No brand selected state
  if (!currentBrand) {
    return (
      <div className="lg:col-span-3">
        <div className="bg-white rounded-2xl p-12 shadow-soft border border-gray-100 text-center">
          <div className="w-20 h-20 bg-gradient-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <Building2 className="w-10 h-10 text-primary-purple" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900 mb-4">
            Welcome to ViralPath.ai
          </h3>
          <p className="text-gray-600 mb-8 max-w-md mx-auto">
            Create your first brand to start generating AI-powered content calendars that drive engagement across all your social media platforms.
          </p>
          <button
            onClick={onBrandManagerOpen}
            className="btn-primary flex items-center gap-2 mx-auto"
          >
            <Plus className="w-4 h-4" />
            Create Your First Brand
          </button>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="lg:col-span-3">
        <div className="bg-white rounded-2xl p-8 shadow-soft border border-gray-100">
          {error.includes('generate') ? (
            <GenerationError onRetry={onGenerateCalendar} />
          ) : error.includes('load') ? (
            <LoadingError
              resource="calendar content"
              onRetry={onRetryLoad}
            />
          ) : (
            <NetworkError onRetry={() => window.location.reload()} />
          )}
        </div>
      </div>
    )
  }

  // Loading state
  if (isLoadingContent) {
    return (
      <div className="lg:col-span-3">
        <CalendarGridSkeleton />
      </div>
    )
  }

  // Content exists
  if (calendarContent.length > 0) {
    return (
      <div className="lg:col-span-3">
        <div className="bg-white rounded-2xl shadow-soft border border-gray-100 overflow-hidden">
          {/* Calendar Header */}
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-neutral-dark">
                  Content Calendar
                </h2>
                <p className="text-sm text-gray-600 mt-1">
                  {currentMonth ? currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' }) : 'Loading...'} • {calendarContent.length} posts
                </p>
              </div>
              <div className="flex items-center gap-2">
                <div className="text-sm text-gray-500">
                  Last updated: {new Date().toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>

          {/* Calendar Grid */}
          <div className="p-6">
            {currentMonth && (
              <CalendarGrid
                content={calendarContent}
                currentMonth={currentMonth}
                onContentClick={onContentClick}
                bulkSelectMode={bulkSelectMode}
                selectedItems={selectedItems}
                onItemSelect={onItemSelect}
              />
            )}
          </div>
        </div>
      </div>
    )
  }

  // Empty state
  return (
    <div className="lg:col-span-3">
      <div className="bg-white rounded-2xl p-12 shadow-soft border border-gray-100 text-center">
        <div className="w-20 h-20 bg-gradient-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
          <Calendar className="w-10 h-10 text-primary-purple" />
        </div>
        <h3 className="text-2xl font-semibold text-gray-900 mb-4">
          Ready to Create Amazing Content?
        </h3>
        <p className="text-gray-600 mb-8 max-w-md mx-auto">
          Generate your first AI-powered content calendar for <strong>{currentBrand.name}</strong>.
          Get 30 days of engaging posts, captions, hashtags, and visual prompts.
        </p>
        <button
          onClick={onGenerateCalendar}
          disabled={isGenerating}
          className="btn-primary flex items-center gap-2 mx-auto"
        >
          {isGenerating ? (
            <RefreshCw className="w-4 h-4 animate-spin" />
          ) : (
            <Plus className="w-4 h-4" />
          )}
          {isGenerating ? 'Generating Amazing Content...' : 'Generate Calendar'}
        </button>

        {isGenerating && (
          <div className="mt-6 text-sm text-gray-500">
            This may take a few moments while our AI creates personalized content for your brand...
          </div>
        )}
      </div>
    </div>
  )
}
