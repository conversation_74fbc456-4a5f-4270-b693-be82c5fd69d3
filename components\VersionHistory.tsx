'use client'

import { useState, useEffect } from 'react'
import { X, History, RotateCcw, Clock, User, FileText, Save } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { contentCalendarService, type ContentVersion, type ContentItem } from '@/lib/services/contentCalendarService'
import { formatDistanceToNow } from 'date-fns'

interface VersionHistoryProps {
  brandId: string
  userId: string
  month: number
  year: number
  onClose: () => void
  onVersionRestore: (content: ContentItem[]) => void
}

export default function VersionHistory({
  brandId,
  userId,
  month,
  year,
  onClose,
  onVersionRestore
}: VersionHistoryProps) {
  const [versions, setVersions] = useState<ContentVersion[]>([])
  const [loading, setLoading] = useState(true)
  const [restoring, setRestoring] = useState<string | null>(null)
  const [previewContent, setPreviewContent] = useState<ContentItem[] | null>(null)
  const [previewVersion, setPreviewVersion] = useState<string | null>(null)

  useEffect(() => {
    loadVersionHistory()
  }, [brandId, userId, month, year])

  const loadVersionHistory = async () => {
    setLoading(true)
    try {
      const history = await contentCalendarService.getVersionHistory(brandId, userId, month, year)
      setVersions(history)
    } catch (error) {
      console.error('Error loading version history:', error)
      toast.error('Failed to load version history')
    } finally {
      setLoading(false)
    }
  }

  const handleRestoreVersion = async (versionId: string) => {
    setRestoring(versionId)
    try {
      const newVersionId = await contentCalendarService.restoreVersion(versionId, userId)
      if (newVersionId) {
        const content = await contentCalendarService.getVersionContent(newVersionId, userId)
        onVersionRestore(content)
        toast.success('Version restored successfully!')
        onClose()
      } else {
        throw new Error('Failed to restore version')
      }
    } catch (error) {
      console.error('Error restoring version:', error)
      toast.error('Failed to restore version')
    } finally {
      setRestoring(null)
    }
  }

  const handlePreviewVersion = async (versionId: string) => {
    if (previewVersion === versionId) {
      setPreviewContent(null)
      setPreviewVersion(null)
      return
    }

    try {
      const content = await contentCalendarService.getVersionContent(versionId, userId)
      setPreviewContent(content)
      setPreviewVersion(versionId)
    } catch (error) {
      console.error('Error loading version content:', error)
      toast.error('Failed to load version content')
    }
  }

  const getVersionIcon = (version: ContentVersion) => {
    if (version.is_current) return <FileText className="w-4 h-4 text-green-600" />
    if (version.auto_saved) return <Save className="w-4 h-4 text-blue-600" />
    return <User className="w-4 h-4 text-gray-600" />
  }

  const getVersionLabel = (version: ContentVersion) => {
    if (version.is_current) return 'Current Version'
    if (version.auto_saved) return 'Auto-saved'
    return 'Manual Save'
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-neutral-dark flex items-center gap-2">
            <History className="w-5 h-5 text-primary-purple" />
            Version History
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-150"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-80px)]">
          {/* Version List */}
          <div className="w-1/2 border-r border-gray-200 overflow-y-auto">
            <div className="p-6">
              <h3 className="font-semibold text-neutral-dark mb-4">
                Versions for {new Date(year, month - 1).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
              </h3>
              
              {loading ? (
                <div className="space-y-3">
                  {Array.from({ length: 5 }).map((_, index) => (
                    <div key={index} className="animate-pulse">
                      <div className="h-16 bg-gray-200 rounded-lg"></div>
                    </div>
                  ))}
                </div>
              ) : versions.length === 0 ? (
                <div className="text-center py-8">
                  <History className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No version history available</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {versions.map((version) => (
                    <div
                      key={version.id}
                      className={`p-4 rounded-lg border transition-all duration-200 cursor-pointer ${
                        version.is_current
                          ? 'border-green-200 bg-green-50'
                          : previewVersion === version.id
                          ? 'border-primary-purple bg-primary-purple/5'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                      onClick={() => handlePreviewVersion(version.id)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3">
                          {getVersionIcon(version)}
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium text-sm">
                                Version {version.version}
                              </span>
                              <span className={`text-xs px-2 py-1 rounded-full ${
                                version.is_current
                                  ? 'bg-green-100 text-green-700'
                                  : version.auto_saved
                                  ? 'bg-blue-100 text-blue-700'
                                  : 'bg-gray-100 text-gray-700'
                              }`}>
                                {getVersionLabel(version)}
                              </span>
                            </div>
                            <p className="text-xs text-gray-600 mb-2">
                              {version.content_preview}
                            </p>
                            <div className="flex items-center gap-2 text-xs text-gray-500">
                              <Clock className="w-3 h-3" />
                              {formatDistanceToNow(new Date(version.created_at), { addSuffix: true })}
                            </div>
                            {version.version_notes && (
                              <p className="text-xs text-gray-600 mt-1 italic">
                                "{version.version_notes}"
                              </p>
                            )}
                          </div>
                        </div>
                        
                        {!version.is_current && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleRestoreVersion(version.id)
                            }}
                            disabled={restoring === version.id}
                            className="btn-secondary text-xs py-1 px-2 flex items-center gap-1"
                          >
                            {restoring === version.id ? (
                              <div className="animate-spin rounded-full h-3 w-3 border-b border-primary-purple"></div>
                            ) : (
                              <RotateCcw className="w-3 h-3" />
                            )}
                            Restore
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Preview Panel */}
          <div className="w-1/2 overflow-y-auto">
            <div className="p-6">
              {previewContent ? (
                <>
                  <h3 className="font-semibold text-neutral-dark mb-4">
                    Preview - Version {versions.find(v => v.id === previewVersion)?.version}
                  </h3>
                  <div className="space-y-4">
                    {previewContent.map((item, index) => (
                      <div key={item.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-sm font-medium text-primary-purple">
                            {new Date(item.date).toLocaleDateString()}
                          </span>
                          <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full capitalize">
                            {item.platform}
                          </span>
                        </div>
                        <p className="text-sm text-gray-800 mb-2 line-clamp-3">
                          {item.caption}
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {item.hashtags.slice(0, 3).map((tag, tagIndex) => (
                            <span key={tagIndex} className="text-xs text-primary-blue">
                              #{tag}
                            </span>
                          ))}
                          {item.hashtags.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{item.hashtags.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              ) : (
                <div className="text-center py-12">
                  <FileText className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">
                    Click on a version to preview its content
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
