'use client'

import { Al<PERSON><PERSON>ircle, RefreshCw, Wifi, WifiOff } from 'lucide-react'

interface ErrorMessageProps {
  title?: string
  message: string
  type?: 'error' | 'warning' | 'network' | 'auth'
  onRetry?: () => void
  retryLabel?: string
  showRetry?: boolean
  className?: string
}

const errorIcons = {
  error: AlertCircle,
  warning: AlertCircle,
  network: WifiOff,
  auth: AlertCircle
}

const errorColors = {
  error: {
    bg: 'bg-red-50',
    border: 'border-red-200',
    icon: 'text-red-500',
    title: 'text-red-800',
    message: 'text-red-700'
  },
  warning: {
    bg: 'bg-yellow-50',
    border: 'border-yellow-200',
    icon: 'text-yellow-500',
    title: 'text-yellow-800',
    message: 'text-yellow-700'
  },
  network: {
    bg: 'bg-blue-50',
    border: 'border-blue-200',
    icon: 'text-blue-500',
    title: 'text-blue-800',
    message: 'text-blue-700'
  },
  auth: {
    bg: 'bg-purple-50',
    border: 'border-purple-200',
    icon: 'text-purple-500',
    title: 'text-purple-800',
    message: 'text-purple-700'
  }
}

export default function ErrorMessage({
  title,
  message,
  type = 'error',
  onRetry,
  retryLabel = 'Try Again',
  showRetry = true,
  className = ''
}: ErrorMessageProps) {
  const Icon = errorIcons[type]
  const colors = errorColors[type]

  return (
    <div className={`rounded-lg border p-4 ${colors.bg} ${colors.border} ${className}`}>
      <div className="flex items-start gap-3">
        <Icon className={`w-5 h-5 mt-0.5 flex-shrink-0 ${colors.icon}`} />
        
        <div className="flex-1 min-w-0">
          {title && (
            <h3 className={`font-medium mb-1 ${colors.title}`}>
              {title}
            </h3>
          )}
          
          <p className={`text-sm leading-relaxed ${colors.message}`}>
            {message}
          </p>
          
          {showRetry && onRetry && (
            <button
              onClick={onRetry}
              className={`mt-3 inline-flex items-center gap-2 text-sm font-medium ${colors.title} hover:underline focus:outline-none focus:underline`}
            >
              <RefreshCw className="w-4 h-4" />
              {retryLabel}
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

// Predefined error message components for common scenarios
export function NetworkError({ onRetry }: { onRetry?: () => void }) {
  return (
    <ErrorMessage
      type="network"
      title="Connection Problem"
      message="Unable to connect to the server. Please check your internet connection and try again."
      onRetry={onRetry}
      retryLabel="Retry Connection"
    />
  )
}

export function AuthError({ onRetry }: { onRetry?: () => void }) {
  return (
    <ErrorMessage
      type="auth"
      title="Authentication Required"
      message="Your session has expired. Please sign in again to continue."
      onRetry={onRetry}
      retryLabel="Sign In"
    />
  )
}

export function LoadingError({ 
  resource = 'data',
  onRetry 
}: { 
  resource?: string
  onRetry?: () => void 
}) {
  return (
    <ErrorMessage
      type="error"
      title="Loading Failed"
      message={`Failed to load ${resource}. This might be a temporary issue.`}
      onRetry={onRetry}
      retryLabel="Reload"
    />
  )
}

export function SaveError({ onRetry }: { onRetry?: () => void }) {
  return (
    <ErrorMessage
      type="warning"
      title="Save Failed"
      message="Your changes couldn't be saved. Please try again or check your connection."
      onRetry={onRetry}
      retryLabel="Try Saving Again"
    />
  )
}

export function GenerationError({ onRetry }: { onRetry?: () => void }) {
  return (
    <ErrorMessage
      type="error"
      title="Generation Failed"
      message="Failed to generate content. This might be due to high demand or a temporary issue with our AI service."
      onRetry={onRetry}
      retryLabel="Try Again"
    />
  )
}
