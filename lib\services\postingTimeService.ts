// Advanced Posting Time Recommendations Service
// Provides sophisticated algorithms for optimal posting times based on multiple factors

interface TimeSlot {
  hour: number
  minute: number
  dayOfWeek: number // 0 = Sunday, 1 = Monday, etc.
}

interface PostingRecommendation {
  time: string // HH:MM format
  dayOfWeek: number
  score: number // 0-100 confidence score
  reasoning: string[]
  timezone: string
}

interface AudienceProfile {
  primaryTimezone: string
  ageGroup: 'gen-z' | 'millennial' | 'gen-x' | 'boomer' | 'mixed'
  workSchedule: 'traditional' | 'flexible' | 'shift' | 'mixed'
  deviceUsage: 'mobile-first' | 'desktop-first' | 'mixed'
}

interface ContentAnalysis {
  type: 'educational' | 'entertainment' | 'promotional' | 'news' | 'lifestyle'
  urgency: 'high' | 'medium' | 'low'
  engagement_type: 'quick-scroll' | 'deep-read' | 'interactive'
  visual_heavy: boolean
}

// Platform-specific optimal time patterns based on research and analytics
const PLATFORM_PATTERNS = {
  instagram: {
    peak_hours: [
      { hour: 11, minute: 0, score: 95 }, // 11 AM - highest engagement
      { hour: 14, minute: 0, score: 90 }, // 2 PM - lunch break
      { hour: 17, minute: 0, score: 85 }, // 5 PM - commute time
      { hour: 19, minute: 0, score: 88 }, // 7 PM - evening scroll
    ],
    best_days: [1, 2, 3, 4, 5], // Monday to Friday
    avoid_hours: [1, 2, 3, 4, 5, 6, 7, 8], // Late night/early morning
    content_factors: {
      visual_heavy: { boost: 15, peak_hours: [12, 17, 19] },
      stories: { boost: 10, peak_hours: [9, 13, 18] }
    }
  },
  tiktok: {
    peak_hours: [
      { hour: 18, minute: 0, score: 95 }, // 6 PM - after work/school
      { hour: 21, minute: 0, score: 92 }, // 9 PM - prime time
      { hour: 12, minute: 0, score: 85 }, // 12 PM - lunch break
      { hour: 15, minute: 0, score: 80 }, // 3 PM - afternoon break
    ],
    best_days: [1, 2, 3, 4], // Monday to Thursday
    avoid_hours: [2, 3, 4, 5, 6, 7, 8, 9], // Late night/early morning
    content_factors: {
      entertainment: { boost: 20, peak_hours: [18, 21] },
      trending: { boost: 25, peak_hours: [19, 20, 21] }
    }
  },
  linkedin: {
    peak_hours: [
      { hour: 8, minute: 0, score: 90 }, // 8 AM - start of workday
      { hour: 12, minute: 0, score: 85 }, // 12 PM - lunch break
      { hour: 17, minute: 0, score: 88 }, // 5 PM - end of workday
      { hour: 19, minute: 0, score: 75 }, // 7 PM - evening professional time
    ],
    best_days: [2, 3, 4], // Tuesday to Thursday
    avoid_hours: [22, 23, 0, 1, 2, 3, 4, 5, 6], // Late night/early morning
    content_factors: {
      professional: { boost: 20, peak_hours: [8, 12, 17] },
      educational: { boost: 15, peak_hours: [9, 13, 18] }
    }
  },
  twitter: {
    peak_hours: [
      { hour: 9, minute: 0, score: 88 }, // 9 AM - morning news check
      { hour: 12, minute: 0, score: 92 }, // 12 PM - lunch break
      { hour: 15, minute: 0, score: 85 }, // 3 PM - afternoon break
      { hour: 21, minute: 0, score: 90 }, // 9 PM - evening engagement
    ],
    best_days: [1, 2, 3, 4, 5], // Monday to Friday
    avoid_hours: [2, 3, 4, 5, 6], // Late night/early morning
    content_factors: {
      news: { boost: 25, peak_hours: [7, 8, 9, 17, 18] },
      trending: { boost: 20, peak_hours: [12, 15, 21] }
    }
  }
}

// Industry-specific posting patterns
const INDUSTRY_PATTERNS = {
  'e-commerce': {
    peak_days: [1, 2, 3, 4, 5], // Weekdays for B2B, weekends for B2C
    peak_hours: [10, 14, 19], // Shopping consideration times
    seasonal_boost: true
  },
  'technology': {
    peak_days: [2, 3, 4], // Mid-week for professional content
    peak_hours: [9, 13, 17], // Business hours
    global_audience: true
  },
  'health-wellness': {
    peak_days: [1, 2, 6, 0], // Monday motivation, weekend planning
    peak_hours: [6, 12, 18], // Morning routine, lunch, evening
    lifestyle_focused: true
  },
  'food-beverage': {
    peak_days: [4, 5, 6, 0], // Weekend planning and dining
    peak_hours: [11, 17, 19], // Meal times
    visual_heavy: true
  }
}

// Audience behavior patterns
const AUDIENCE_PATTERNS = {
  'gen-z': {
    peak_hours: [15, 18, 21, 23], // After school/work, evening, late night
    platforms: ['tiktok', 'instagram'],
    mobile_first: true,
    attention_span: 'short'
  },
  'millennial': {
    peak_hours: [8, 12, 17, 20], // Commute, lunch, after work, evening
    platforms: ['instagram', 'twitter', 'linkedin'],
    device_mixed: true,
    attention_span: 'medium'
  },
  'gen-x': {
    peak_hours: [7, 12, 18], // Morning, lunch, evening
    platforms: ['linkedin', 'twitter'],
    desktop_preference: true,
    attention_span: 'long'
  },
  'boomer': {
    peak_hours: [9, 14, 19], // Morning, afternoon, early evening
    platforms: ['linkedin'],
    desktop_preference: true,
    attention_span: 'long'
  },
  'mixed': {
    peak_hours: [8, 12, 17, 20], // General peak times across all demographics
    platforms: ['instagram', 'twitter', 'linkedin'],
    device_mixed: true,
    attention_span: 'medium'
  }
}

export class PostingTimeService {
  /**
   * Get optimal posting times for a specific platform and content
   */
  static getOptimalTimes(
    platform: string,
    contentType: string,
    audienceProfile: AudienceProfile,
    industry: string,
    timezone: string = 'America/New_York'
  ): PostingRecommendation[] {
    const platformPattern = PLATFORM_PATTERNS[platform as keyof typeof PLATFORM_PATTERNS]
    const industryPattern = INDUSTRY_PATTERNS[industry.toLowerCase() as keyof typeof INDUSTRY_PATTERNS]
    const audiencePattern = AUDIENCE_PATTERNS[audienceProfile.ageGroup]

    if (!platformPattern) {
      throw new Error(`Platform ${platform} not supported`)
    }

    const recommendations: PostingRecommendation[] = []

    // Generate recommendations for the next 7 days
    for (let dayOffset = 0; dayOffset < 7; dayOffset++) {
      const dayOfWeek = (new Date().getDay() + dayOffset) % 7

      // Calculate day score based on platform and industry patterns
      const dayScore = this.calculateDayScore(dayOfWeek, platformPattern, industryPattern)

      if (dayScore < 30) continue // Skip low-scoring days

      // Get optimal hours for this day
      const optimalHours = this.getOptimalHoursForDay(
        dayOfWeek,
        platformPattern,
        audiencePattern,
        contentType
      )

      optimalHours.forEach(hourData => {
        const totalScore = this.calculateTotalScore(
          hourData.score,
          dayScore,
          contentType,
          platform,
          audienceProfile,
          industry
        )

        const reasoning = this.generateReasoning(
          platform,
          hourData.hour,
          dayOfWeek,
          contentType,
          audienceProfile,
          industry,
          totalScore
        )

        recommendations.push({
          time: `${hourData.hour.toString().padStart(2, '0')}:${hourData.minute.toString().padStart(2, '0')}`,
          dayOfWeek,
          score: Math.round(totalScore),
          reasoning,
          timezone
        })
      })
    }

    // Sort by score and return top recommendations
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, 10) // Return top 10 recommendations
  }

  private static calculateDayScore(
    dayOfWeek: number,
    platformPattern: any,
    industryPattern: any
  ): number {
    let score = 50 // Base score

    // Platform day preference
    if (platformPattern.best_days.includes(dayOfWeek)) {
      score += 30
    }

    // Industry day preference
    if (industryPattern?.peak_days?.includes(dayOfWeek)) {
      score += 20
    }

    // Weekend vs weekday adjustments
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6
    if (isWeekend) {
      // Most B2B content performs worse on weekends
      if (['linkedin'].includes(platformPattern)) {
        score -= 25
      }
      // B2C content might perform better on weekends
      if (['instagram', 'tiktok'].includes(platformPattern)) {
        score += 10
      }
    }

    return Math.max(0, Math.min(100, score))
  }

  private static getOptimalHoursForDay(
    dayOfWeek: number,
    platformPattern: any,
    audiencePattern: any,
    contentType: string
  ): Array<{ hour: number; minute: number; score: number }> {
    const hours = [...platformPattern.peak_hours]

    // Adjust based on audience patterns
    if (audiencePattern) {
      audiencePattern.peak_hours.forEach((hour: number) => {
        const existingHour = hours.find(h => h.hour === hour)
        if (existingHour) {
          existingHour.score += 10
        } else {
          hours.push({ hour, minute: 0, score: 70 })
        }
      })
    }

    // Content type adjustments
    if (platformPattern.content_factors) {
      const contentFactor = platformPattern.content_factors[contentType]
      if (contentFactor) {
        contentFactor.peak_hours.forEach((hour: number) => {
          const existingHour = hours.find(h => h.hour === hour)
          if (existingHour) {
            existingHour.score += contentFactor.boost
          }
        })
      }
    }

    return hours.filter(h => !platformPattern.avoid_hours.includes(h.hour))
  }

  private static calculateTotalScore(
    baseScore: number,
    dayScore: number,
    contentType: string,
    platform: string,
    audienceProfile: AudienceProfile,
    industry: string
  ): number {
    let totalScore = (baseScore + dayScore) / 2

    // Content type bonuses
    const contentBonus = this.getContentTypeBonus(contentType, platform)
    totalScore += contentBonus

    // Audience alignment bonus
    const audienceBonus = this.getAudienceAlignmentBonus(audienceProfile, platform)
    totalScore += audienceBonus

    // Industry alignment bonus
    const industryBonus = this.getIndustryAlignmentBonus(industry, platform)
    totalScore += industryBonus

    return Math.max(0, Math.min(100, totalScore))
  }

  private static getContentTypeBonus(contentType: string, platform: string): number {
    const bonuses: Record<string, Record<string, number>> = {
      educational: { linkedin: 15, twitter: 10, instagram: 5 },
      entertainment: { tiktok: 20, instagram: 15, twitter: 5 },
      promotional: { instagram: 10, linkedin: 5, twitter: 8 },
      news: { twitter: 20, linkedin: 10, instagram: 5 },
      lifestyle: { instagram: 18, tiktok: 12, twitter: 5 }
    }

    return bonuses[contentType]?.[platform] || 0
  }

  private static getAudienceAlignmentBonus(audienceProfile: AudienceProfile, platform: string): number {
    const audiencePattern = AUDIENCE_PATTERNS[audienceProfile.ageGroup]
    if (audiencePattern?.platforms.includes(platform)) {
      return 15
    }
    return 0
  }

  private static getIndustryAlignmentBonus(industry: string, platform: string): number {
    const industryBonus: Record<string, Record<string, number>> = {
      'technology': { linkedin: 15, twitter: 12 },
      'e-commerce': { instagram: 15, tiktok: 10 },
      'health-wellness': { instagram: 12, tiktok: 8 },
      'food-beverage': { instagram: 18, tiktok: 15 }
    }

    return industryBonus[industry.toLowerCase()]?.[platform] || 0
  }

  private static generateReasoning(
    platform: string,
    hour: number,
    dayOfWeek: number,
    contentType: string,
    audienceProfile: AudienceProfile,
    industry: string,
    score: number
  ): string[] {
    const reasons: string[] = []
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    const dayName = dayNames[dayOfWeek]

    // Time-based reasoning
    if (hour >= 6 && hour <= 9) {
      reasons.push(`Morning engagement peak (${hour}:00) - users check ${platform} during commute`)
    } else if (hour >= 11 && hour <= 14) {
      reasons.push(`Lunch break peak (${hour}:00) - high mobile usage during midday break`)
    } else if (hour >= 17 && hour <= 19) {
      reasons.push(`After-work peak (${hour}:00) - users unwind and browse social media`)
    } else if (hour >= 20 && hour <= 22) {
      reasons.push(`Evening prime time (${hour}:00) - peak leisure browsing hours`)
    }

    // Day-based reasoning
    if ([1, 2, 3, 4].includes(dayOfWeek)) {
      reasons.push(`${dayName} shows strong engagement for ${platform} content`)
    } else if ([5, 6].includes(dayOfWeek)) {
      reasons.push(`Weekend timing - good for lifestyle and entertainment content`)
    }

    // Audience-based reasoning
    const audiencePattern = AUDIENCE_PATTERNS[audienceProfile.ageGroup]
    if (audiencePattern?.peak_hours.includes(hour)) {
      reasons.push(`Optimal for ${audienceProfile.ageGroup} audience behavior patterns`)
    }

    // Content-based reasoning
    if (contentType === 'educational' && [8, 12, 17].includes(hour)) {
      reasons.push(`Educational content performs well during focused browsing times`)
    } else if (contentType === 'entertainment' && [18, 21].includes(hour)) {
      reasons.push(`Entertainment content peaks during leisure hours`)
    }

    // Industry-based reasoning
    if (industry === 'technology' && [9, 13, 17].includes(hour)) {
      reasons.push(`Technology industry content aligns with professional browsing patterns`)
    }

    // Score-based reasoning
    if (score >= 90) {
      reasons.push(`Exceptional timing - multiple factors align for maximum engagement`)
    } else if (score >= 80) {
      reasons.push(`Strong timing - good alignment of audience and platform patterns`)
    } else if (score >= 70) {
      reasons.push(`Good timing - solid engagement potential`)
    }

    return reasons.slice(0, 3) // Return top 3 most relevant reasons
  }

  /**
   * Get quick recommendations for immediate posting
   */
  static getQuickRecommendations(platform: string, timezone: string = 'America/New_York'): PostingRecommendation[] {
    const now = new Date()
    const currentHour = now.getHours()
    const dayOfWeek = now.getDay()

    const platformPattern = PLATFORM_PATTERNS[platform as keyof typeof PLATFORM_PATTERNS]
    if (!platformPattern) return []

    const recommendations: PostingRecommendation[] = []

    // Check next few hours for good posting times
    for (let hourOffset = 0; hourOffset < 6; hourOffset++) {
      const targetHour = (currentHour + hourOffset) % 24
      const peakHour = platformPattern.peak_hours.find(p => p.hour === targetHour)

      if (peakHour && !platformPattern.avoid_hours.includes(targetHour)) {
        recommendations.push({
          time: `${targetHour.toString().padStart(2, '0')}:00`,
          dayOfWeek,
          score: peakHour.score,
          reasoning: [`Peak ${platform} engagement time`, `Good immediate posting opportunity`],
          timezone
        })
      }
    }

    return recommendations.sort((a, b) => b.score - a.score).slice(0, 3)
  }
}
