# ViralPath.ai - Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- 🎨 **Favicon Support** - Added modern gradient favicon with SVG and ICO formats for better browser compatibility
- 📋 **Task Management System** - Created tasks.md for tracking current sprint tasks separate from long-term TODO.md roadmap
- 🗄️ **Database Content Persistence** - Migrated calendar content from localStorage to Supabase database
- 📊 **Content Calendar Service** - New service layer for managing calendar content with proper database operations
- 🔄 **Auto-loading Calendar Content** - Calendar content now loads automatically when switching brands or months
- 📈 **Content Statistics** - Basic content statistics and platform breakdown functionality
- 💾 **Auto-save for Content Edits** - Automatic saving with 2-second debounce when editing content in ContentModal
- 🎯 **Debounced Save Hook** - Custom React hook for debounced auto-save functionality to prevent excessive API calls
- 📊 **Auto-save Status Indicator** - Visual feedback showing save status (saving/saved) in content editor
- 🛡️ **Comprehensive Error Handling** - Error boundaries, retry mechanisms, and user-friendly error messages
- 🔄 **Retry Logic with Backoff** - Automatic retry for failed API operations with exponential backoff
- 🎨 **Error Message Components** - Reusable error components for different error types (network, auth, loading, etc.)
- ⚠️ **Error State Management** - Proper error state handling throughout the application
- 💀 **Skeleton Loading Components** - Professional skeleton loaders for calendar, stats, brands, and content
- 🎭 **Generation Progress Modal** - Step-by-step progress indicator for AI content generation with animations
- 📊 **Enhanced Loading States** - Improved loading feedback throughout the application
- 🎨 **Progress Bar Component** - Reusable progress bar with smooth animations
- 📚 **Content Versioning System** - Complete version tracking with history, restore, and preview functionality
- 🔄 **Version History Modal** - Interactive interface to view, preview, and restore previous content versions
- 🗄️ **Database Versioning** - Advanced database schema with triggers for automatic version management
- 📝 **Version Notes** - Automatic and manual version notes for tracking changes
- 🤖 **Enhanced AI Content Generation** - Advanced prompts with industry-specific templates and tone guidelines
- 🎯 **Content Variations Generator** - AI-powered A/B testing with multiple content variations
- 📊 **Content Performance Analysis** - Engagement, viral potential, and brand alignment scoring
- 🏭 **Industry-Specific Templates** - Tailored content strategies for E-commerce, Technology, Health & Wellness, and Food & Beverage
- 🎨 **Platform Engagement Tactics** - Specialized strategies for each social media platform
- 🎨 **Complete UI/UX Redesign** - Modern, professional interface with improved user experience
- 🏠 **Enhanced Home Page** - Comprehensive landing page with testimonials, features, and social proof
- 📱 **Modern Dashboard Design** - Redesigned dashboard with better navigation, cards, and visual hierarchy
- ✨ **Improved Visual Design** - Updated color schemes, typography, and component styling
- 🎯 **Better Information Architecture** - Reorganized content and improved user flow
- 🔧 **Dashboard Refactoring** - Modular component architecture with separated concerns
- 📦 **Component Modularity** - Split dashboard into reusable header, sidebar, main content, and modal components
- 🛠️ **Improved Maintainability** - Better code organization and easier feature development
- 🔄 **Enhanced Reusability** - Modular components that can be easily extended and modified
- 🐛 **Fixed Critical Errors** - Resolved parsing errors and hydration mismatches
- ⚡ **Performance Optimization** - Fixed SSR/client hydration issues for better performance
- 🔧 **Error Handling** - Improved error boundaries and null state handling
- ⏰ **Advanced Posting Time Recommendations** - AI-powered optimal posting times based on platform, audience, and industry
- 🎯 **Smart Scheduling Algorithm** - Sophisticated algorithm considering multiple factors for maximum engagement
- 📊 **Audience Behavior Analysis** - Platform-specific recommendations based on demographic and behavioral patterns
- 🕐 **Real-time Posting Suggestions** - Quick recommendations for immediate posting opportunities
- 📚 **Content Template Library** - Professional pre-built templates for different industries and content types
- 🎨 **Template Customization System** - Interactive template generator with customizable variables and real-time preview
- 🔄 **Content Variations Generator** - AI-powered system to create multiple variations from single templates
- 📝 **Industry-Specific Templates** - Specialized templates for educational, promotional, inspirational, and behind-the-scenes content
- ☑️ **Bulk Content Operations** - Comprehensive bulk operations system for managing multiple content items efficiently
- 🔧 **Bulk Edit & Regenerate** - Edit properties and regenerate content for multiple items simultaneously
- 📤 **Bulk Export System** - Export content to CSV, JSON with customizable field selection
- 📋 **Advanced Selection Interface** - Intuitive checkbox-based selection with visual feedback and progress tracking
- 🔄 **OpenRouter Integration** - Migrated from OpenAI to OpenRouter with openai/gpt-oss-20b:free (free model)
- 🆓 **Free AI Content Generation** - No API costs with openai/gpt-oss-20b:free model integration
- 🚀 **Enhanced Bulk Regeneration** - Real AI-powered bulk content regeneration using OpenRouter
- 🔧 **Improved Error Handling** - Better fallback mechanisms for AI generation failures
- 🐛 **Fixed Platform Name Mapping** - Resolved platform configuration errors with proper name normalization
- 🔧 **Enhanced Debugging** - Added comprehensive logging for AI service troubleshooting
- ⚡ **Improved Hydration** - Fixed SSR hydration mismatches and null reference errors
- 🛡️ **Robust Fallback System** - Better error handling with platform-specific fallback content
- 🎯 **Model Specification Update** - Updated to use correct OpenRouter model name: openai/gpt-oss-20b:free
- 🔧 **Enhanced JSON Parsing** - Robust AI response parsing with markdown code block handling
- 📝 **Improved AI Prompts** - More explicit JSON format requirements for better response quality
- ✅ **Response Validation** - Added validation for required fields in AI responses
- 🔄 **Type System Alignment** - Unified ContentItem interface across all services
- 🚫 **Rate Limit Handling** - Graceful handling of OpenRouter free tier rate limits
- 🎭 **Demo Mode** - High-quality fallback content generation when AI service is unavailable
- ⏱️ **Request Throttling** - Added delays between API calls to prevent rate limiting
- 🔄 **Smart Fallback Chain** - Automatic switching to demo mode when rate limits are hit
- 📢 **User-Friendly Messages** - Clear notifications when using demo content instead of AI
- 🎛️ **AI Model Selection** - Choose from multiple OpenRouter models (GPT-4, Claude, Llama, etc.)
- 🔧 **Model Settings Service** - Persistent model preferences with localStorage
- 💰 **Cost Transparency** - Clear pricing information for each model tier
- 🎯 **Model Optimization** - Automatic parameter tuning per model type
- 📊 **Model Status Indicator** - Real-time display of current AI model in header
- ⚙️ **Advanced Model Settings** - Temperature, max tokens, and top-p configuration
- 🔧 **Model Endpoint Fixes** - Removed invalid Llama model, added working DeepSeek V3 0324
- 🆕 **Additional Free Models** - Added WizardLM 2 8x22B and Gemma 2 9B options
- 🛡️ **Enhanced Error Handling** - Automatic fallback when selected model is unavailable
- 🔄 **Smart Model Switching** - Auto-switch to default model on 404 errors
- ✅ **DeepSeek Model ID Fix** - Updated to correct model ID: deepseek/deepseek-chat-v3-0324:free
- 🚀 **Google Gemini Integration** - Added Gemini 1.5 Flash and Gemini 1.5 Pro models
- 🔧 **Gemini Service** - Dedicated service for Google AI API integration
- 🎯 **Multimodal Support** - Gemini models support advanced multimodal capabilities
- 💰 **Cost-Effective Options** - Gemini 1.5 Flash offers very competitive pricing
- 🛡️ **Dual API Support** - Seamless switching between OpenRouter and Google AI APIs
- 🚀 **Gemini 2.0 Flash Integration** - Added latest Google Gemini 2.0 Flash model for enhanced performance
- 🎯 **Viral Prompt Engineering Framework** - Comprehensive system for viral content generation
- 🧠 **Psychology-Driven Content** - AI prompts optimized with viral psychology triggers
- 📊 **Platform-Specific Optimization** - Tailored prompts for each social media platform
- 🔥 **Enhanced Viral Elements** - Hooks, pattern interrupts, and engagement drivers built-in

### Changed
- 🔧 **Dashboard Data Flow** - Updated dashboard to use database persistence instead of localStorage
- 🔧 **Content Management** - All content operations now use Supabase for reliable data persistence
- 🔧 **Brand Switching** - Improved brand switching to automatically load associated calendar content

### Deprecated
- 📦 **localStorage for Calendar Content** - Replaced with proper database storage

### Removed
- Nothing yet

### Fixed
- 🚨 **Supabase 406 Errors** - Resolved "Not Acceptable" errors caused by conflicting Row Level Security policies on content_calendars table
- 🔐 **Authentication Flow** - Enhanced dashboard authentication with better session checking and error handling
- 🌐 **Favicon 404 Error** - Fixed missing favicon.ico causing browser console errors
- 🐛 **Data Persistence Issues** - Calendar content now persists properly across sessions and devices
- 🐛 **Database Type Consistency** - Fixed content_calendars table column naming in TypeScript types

### Security
- 🔒 **Row Level Security** - Calendar content is protected by RLS policies ensuring users only access their own data

---

## [0.2.0] - 2024-12-19

### Added
- ✅ **Next.js Upgrade to v15.4.5** - Upgraded from Next.js 14.0.4 to latest stable version
- ✅ **React 19 Support** - Updated React and React DOM to v19.1.1
- ✅ **Turbopack Integration** - Enabled Turbopack for faster development builds
- ✅ **Enhanced Development Experience** - Improved build performance and hot reload
- 📝 **Project Documentation** - Added comprehensive README.md with setup instructions
- 📝 **Development Tracking** - Created TODO.md for feature planning
- 📝 **Version History** - Initialized CHANGELOG.md for release tracking

### Changed
- 🔧 **Next.js Configuration** - Removed deprecated `appDir` experimental flag
- 🔧 **Package Dependencies** - Updated ESLint config to v15.4.5
- 🔧 **Development Script** - Added Turbopack flag to dev command

### Fixed
- 🐛 **Configuration Warnings** - Resolved Next.js config deprecation warnings
- 🐛 **Dependency Conflicts** - Resolved peer dependency warnings

---

## [0.1.0] - 2024-12-19 - Initial Release

### Added
- 🎨 **Landing Page** - Beautiful hero section with gradient design
- 🎨 **Responsive Header** - Navigation with mobile menu support
- 🎨 **Footer Component** - Links and social media integration
- 🚀 **Onboarding Modal** - Multi-step brand setup process
  - Brand name and industry selection
  - Target audience definition
  - Content tone configuration
  - Social media platform selection
- 📅 **Dashboard Page** - Content calendar management interface
- 📅 **Calendar Grid** - Monthly view with content visualization
  - Platform-specific icons (Instagram, TikTok, LinkedIn, Twitter)
  - Content preview with captions
  - Optimal posting time indicators
  - Current day highlighting
- ✏️ **Content Modal** - Individual content editing interface
  - Caption and hashtag editing
  - Visual prompt suggestions
  - Best posting time recommendations
  - Platform-specific AI tips
  - Copy-to-clipboard functionality
- 📤 **Export Modal** - Multiple export format support
  - CSV export for spreadsheets
  - JSON export for developers
  - ICS export for calendar applications
- 🤖 **AI Service Integration** - OpenRouter with openai/gpt-oss-20b:free powered content generation
  - Platform-optimized content creation
  - Hashtag recommendations
  - Visual content prompts
  - Optimal posting time suggestions
  - Fallback content generation
- 🎨 **Design System** - Custom Tailwind CSS configuration
  - Purple/blue gradient color palette
  - Inter and Poppins font integration
  - Custom animations (fade-in, slide-up, shimmer)
  - Responsive grid system
  - Consistent component styling

### Technical Stack
- ⚡ **Frontend**: Next.js 14.0.4 with App Router
- ⚛️ **UI Framework**: React 18 with TypeScript
- 🎨 **Styling**: Tailwind CSS with custom design system
- 🤖 **AI Integration**: OpenRouter with openai/gpt-oss-20b:free API (free model)
- 🗄️ **Database Ready**: Supabase configuration (not yet implemented)
- 📱 **Icons**: Lucide React icon library
- 🔥 **Notifications**: React Hot Toast
- 📅 **Date Handling**: date-fns library
- 🎭 **UI Components**: Headless UI for accessibility

### Project Structure
```
viralpath-ai/
├── app/                    # Next.js app directory
│   ├── dashboard/         # Dashboard pages
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # Reusable components
│   ├── CalendarGrid.tsx   # Calendar display
│   ├── ContentModal.tsx   # Content editing
│   ├── ExportModal.tsx    # Export functionality
│   ├── Header.tsx         # Navigation
│   ├── Footer.tsx         # Footer
│   └── OnboardingModal.tsx # Brand setup
├── lib/                   # Utility functions
│   └── ai-service.ts      # AI content generation
└── Configuration files    # Next.js, Tailwind, TypeScript configs
```

### Development Setup
- 📦 **Package Manager**: npm with package-lock.json
- 🔧 **Configuration**: TypeScript, ESLint, PostCSS, Tailwind
- 🌍 **Environment**: .env.local for API keys
- 📝 **Documentation**: Comprehensive README with setup instructions
- 🚫 **Git Ignore**: Proper exclusions for Node.js and Next.js

---

## Release Notes Format

### Categories
- **Added** for new features
- **Changed** for changes in existing functionality
- **Deprecated** for soon-to-be removed features
- **Removed** for now removed features
- **Fixed** for any bug fixes
- **Security** in case of vulnerabilities

### Emoji Legend
- 🎨 UI/UX improvements
- 🚀 New features
- 📅 Calendar/scheduling related
- 🤖 AI/ML features
- 🔧 Configuration changes
- 🐛 Bug fixes
- ⚡ Performance improvements
- 📝 Documentation
- 🔒 Security updates
- 📦 Dependencies
- 🌐 Internationalization
- 📱 Mobile improvements
- 🧪 Testing
- ♿ Accessibility

---

## Versioning Strategy

- **Major (X.0.0)**: Breaking changes, major feature releases
- **Minor (0.X.0)**: New features, non-breaking changes
- **Patch (0.0.X)**: Bug fixes, small improvements

## Release Schedule

- **Major releases**: Quarterly (Q1, Q2, Q3, Q4)
- **Minor releases**: Monthly or bi-weekly
- **Patch releases**: As needed for critical fixes

---

*For the complete roadmap and planned features, see [TODO.md](./todo.md)*
*For setup instructions and documentation, see [README.md](./README.md)*