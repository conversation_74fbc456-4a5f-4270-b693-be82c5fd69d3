// Bulk Operations Service
// Handles bulk operations on multiple content items for improved workflow efficiency

import { ContentItem } from '@/lib/types/content'
import { generateSinglePost } from '@/lib/ai-service'

export interface BulkOperation {
  id: string
  type: 'edit' | 'regenerate' | 'export' | 'delete' | 'schedule' | 'duplicate'
  status: 'pending' | 'processing' | 'completed' | 'failed'
  itemIds: string[]
  parameters: Record<string, any>
  progress: number
  createdAt: Date
  completedAt?: Date
  error?: string
}

export interface BulkEditOptions {
  platform?: string
  status?: 'draft' | 'scheduled' | 'published'
  hashtags?: {
    action: 'add' | 'remove' | 'replace'
    tags: string[]
  }
  bestTime?: string
  addPrefix?: string
  addSuffix?: string
}

export interface BulkExportOptions {
  format: 'csv' | 'json' | 'google-sheets'
  includeFields: string[]
  filename?: string
  dateRange?: {
    start: string
    end: string
  }
}

export class BulkOperationsService {
  private static operations: BulkOperation[] = []

  /**
   * Create a new bulk operation
   */
  static createOperation(
    type: BulkOperation['type'],
    itemIds: string[],
    parameters: Record<string, any>
  ): BulkOperation {
    const operation: BulkOperation = {
      id: `bulk_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      type,
      status: 'pending',
      itemIds,
      parameters,
      progress: 0,
      createdAt: new Date()
    }

    this.operations.push(operation)
    return operation
  }

  /**
   * Execute bulk edit operation
   */
  static async executeBulkEdit(
    items: ContentItem[],
    options: BulkEditOptions,
    onProgress?: (progress: number) => void
  ): Promise<ContentItem[]> {
    const operation = this.createOperation('edit', items.map(i => i.id), options)

    try {
      operation.status = 'processing'
      const updatedItems: ContentItem[] = []

      for (let i = 0; i < items.length; i++) {
        const item = { ...items[i] }

        // Apply platform change
        if (options.platform) {
          item.platform = options.platform
        }

        // Apply status change
        if (options.status) {
          item.status = options.status
        }

        // Apply hashtag changes
        if (options.hashtags) {
          switch (options.hashtags.action) {
            case 'add':
              item.hashtags = Array.from(new Set([...item.hashtags, ...options.hashtags.tags]))
              break
            case 'remove':
              item.hashtags = item.hashtags.filter(tag => !options.hashtags!.tags.includes(tag))
              break
            case 'replace':
              item.hashtags = options.hashtags.tags
              break
          }
        }

        // Apply best time change
        if (options.bestTime) {
          item.bestTime = options.bestTime
        }

        // Apply content prefix/suffix
        if (options.addPrefix) {
          item.caption = options.addPrefix + ' ' + item.caption
        }
        if (options.addSuffix) {
          item.caption = item.caption + ' ' + options.addSuffix
        }

        updatedItems.push(item)

        // Update progress
        const progress = Math.round(((i + 1) / items.length) * 100)
        operation.progress = progress
        onProgress?.(progress)

        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      operation.status = 'completed'
      operation.completedAt = new Date()
      operation.progress = 100

      return updatedItems
    } catch (error) {
      operation.status = 'failed'
      operation.error = error instanceof Error ? error.message : 'Unknown error'
      throw error
    }
  }

  /**
   * Execute bulk regeneration using AI
   */
  static async executeBulkRegenerate(
    items: ContentItem[],
    brandData: any,
    onProgress?: (progress: number) => void
  ): Promise<ContentItem[]> {
    const operation = this.createOperation('regenerate', items.map(i => i.id), { brandData })

    try {
      operation.status = 'processing'
      const regeneratedItems: ContentItem[] = []

      for (let i = 0; i < items.length; i++) {
        const item = { ...items[i] }

        try {
          // Use real AI regeneration
          const regeneratedItem = await generateSinglePost(
            {
              brandName: brandData.name || 'Brand',
              industry: brandData.industry || 'general',
              tone: brandData.tone || 'professional',
              targetAudience: brandData.targetAudience || 'general audience',
              platforms: [item.platform]
            },
            new Date(item.date),
            item.platform
          )

          // Keep original ID and date, but update content
          item.caption = regeneratedItem.caption
          item.hashtags = regeneratedItem.hashtags
          item.visualPrompt = regeneratedItem.visualPrompt
          item.bestTime = regeneratedItem.bestTime
        } catch (error) {
          console.error('AI regeneration failed, using fallback:', error)

          // Fallback to simple variation if AI fails
          const variations = [
            'Discover the power of',
            'Unlock the secrets of',
            'Master the art of',
            'Transform your approach to',
            'Elevate your game with'
          ]

          const randomVariation = variations[Math.floor(Math.random() * variations.length)]

          if (item.caption.length > 50) {
            item.caption = randomVariation + ' ' + item.caption.substring(item.caption.indexOf(' ') + 1)
          }

          // Add some new hashtags
          const newHashtags = ['#fresh', '#updated', '#new2024', '#trending']
          const randomHashtag = newHashtags[Math.floor(Math.random() * newHashtags.length)]
          if (!item.hashtags.includes(randomHashtag)) {
            item.hashtags.push(randomHashtag)
          }
        }

        regeneratedItems.push(item)

        // Update progress
        const progress = Math.round(((i + 1) / items.length) * 100)
        operation.progress = progress
        onProgress?.(progress)
      }

      operation.status = 'completed'
      operation.completedAt = new Date()
      operation.progress = 100

      return regeneratedItems
    } catch (error) {
      operation.status = 'failed'
      operation.error = error instanceof Error ? error.message : 'Unknown error'
      throw error
    }
  }

  /**
   * Execute bulk export
   */
  static async executeBulkExport(
    items: ContentItem[],
    options: BulkExportOptions,
    onProgress?: (progress: number) => void
  ): Promise<string> {
    const operation = this.createOperation('export', items.map(i => i.id), options)

    try {
      operation.status = 'processing'

      // Filter items by date range if specified
      let filteredItems = items
      if (options.dateRange) {
        filteredItems = items.filter(item => {
          const itemDate = new Date(item.date)
          const startDate = new Date(options.dateRange!.start)
          const endDate = new Date(options.dateRange!.end)
          return itemDate >= startDate && itemDate <= endDate
        })
      }

      let exportData: string = ''

      if (options.format === 'csv') {
        // Generate CSV
        const headers = options.includeFields.join(',')
        const rows = filteredItems.map(item => {
          return options.includeFields.map(field => {
            const value = this.getFieldValue(item, field)
            return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value
          }).join(',')
        })

        exportData = [headers, ...rows].join('\n')
      } else if (options.format === 'json') {
        // Generate JSON
        const exportItems = filteredItems.map(item => {
          const exportItem: any = {}
          options.includeFields.forEach(field => {
            exportItem[field] = this.getFieldValue(item, field)
          })
          return exportItem
        })

        exportData = JSON.stringify(exportItems, null, 2)
      }

      // Simulate export processing
      for (let i = 0; i <= 100; i += 10) {
        operation.progress = i
        onProgress?.(i)
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      operation.status = 'completed'
      operation.completedAt = new Date()
      operation.progress = 100

      return exportData
    } catch (error) {
      operation.status = 'failed'
      operation.error = error instanceof Error ? error.message : 'Unknown error'
      throw error
    }
  }

  /**
   * Execute bulk duplicate
   */
  static async executeBulkDuplicate(
    items: ContentItem[],
    targetDate?: string,
    onProgress?: (progress: number) => void
  ): Promise<ContentItem[]> {
    const operation = this.createOperation('duplicate', items.map(i => i.id), { targetDate })

    try {
      operation.status = 'processing'
      const duplicatedItems: ContentItem[] = []

      for (let i = 0; i < items.length; i++) {
        const item = { ...items[i] }

        // Create duplicate with new ID and date
        const duplicate: ContentItem = {
          ...item,
          id: `dup_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          date: targetDate || new Date().toISOString().split('T')[0],
          status: 'draft',
          engagement: { likes: 0, comments: 0, shares: 0 }
        }

        duplicatedItems.push(duplicate)

        // Update progress
        const progress = Math.round(((i + 1) / items.length) * 100)
        operation.progress = progress
        onProgress?.(progress)

        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 200))
      }

      operation.status = 'completed'
      operation.completedAt = new Date()
      operation.progress = 100

      return duplicatedItems
    } catch (error) {
      operation.status = 'failed'
      operation.error = error instanceof Error ? error.message : 'Unknown error'
      throw error
    }
  }

  /**
   * Execute bulk delete
   */
  static async executeBulkDelete(
    itemIds: string[],
    onProgress?: (progress: number) => void
  ): Promise<void> {
    const operation = this.createOperation('delete', itemIds, {})

    try {
      operation.status = 'processing'

      for (let i = 0; i < itemIds.length; i++) {
        // Simulate deletion processing
        await new Promise(resolve => setTimeout(resolve, 100))

        // Update progress
        const progress = Math.round(((i + 1) / itemIds.length) * 100)
        operation.progress = progress
        onProgress?.(progress)
      }

      operation.status = 'completed'
      operation.completedAt = new Date()
      operation.progress = 100
    } catch (error) {
      operation.status = 'failed'
      operation.error = error instanceof Error ? error.message : 'Unknown error'
      throw error
    }
  }

  /**
   * Get field value from content item
   */
  private static getFieldValue(item: ContentItem, field: string): any {
    switch (field) {
      case 'id': return item.id
      case 'date': return item.date
      case 'platform': return item.platform
      case 'content': return item.caption
      case 'hashtags': return item.hashtags.join(' ')
      case 'visualPrompt': return item.visualPrompt
      case 'status': return item.status
      case 'likes': return item.engagement.likes
      case 'comments': return item.engagement.comments
      case 'shares': return item.engagement.shares
      case 'bestTime': return item.bestTime
      default: return ''
    }
  }

  /**
   * Get operation by ID
   */
  static getOperation(id: string): BulkOperation | null {
    return this.operations.find(op => op.id === id) || null
  }

  /**
   * Get all operations
   */
  static getAllOperations(): BulkOperation[] {
    return this.operations.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
  }

  /**
   * Clear completed operations
   */
  static clearCompletedOperations(): void {
    this.operations = this.operations.filter(op => op.status !== 'completed')
  }

  /**
   * Get operation statistics
   */
  static getOperationStats(): {
    total: number
    completed: number
    failed: number
    processing: number
  } {
    const total = this.operations.length
    const completed = this.operations.filter(op => op.status === 'completed').length
    const failed = this.operations.filter(op => op.status === 'failed').length
    const processing = this.operations.filter(op => op.status === 'processing').length

    return { total, completed, failed, processing }
  }
}
