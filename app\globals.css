@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #F5F7FA;
  --foreground: #2E3148;
  --font-inter: 'Inter', sans-serif;
  --font-poppins: 'Poppins', sans-serif;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #2E3148;
    --foreground: #F5F7FA;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #5E2CED 0%, #1FB6FF 100%);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #5E2CED;
}

/* Loading shimmer effect */
.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 2s infinite;
}

/* Button hover effects */
.btn-primary {
  @apply bg-gradient-primary text-white font-medium px-6 py-3 rounded-lg transition-all duration-150 hover:scale-105 hover:shadow-lg;
}

.btn-secondary {
  @apply border-2 border-primary-purple text-primary-purple font-medium px-6 py-3 rounded-lg transition-all duration-150 hover:bg-primary-purple hover:text-white;
}

/* Calendar grid styles */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background-color: #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.calendar-cell {
  @apply bg-white p-3 min-h-[120px] transition-all duration-150 hover:bg-gray-50 cursor-pointer;
}

.calendar-cell.selected {
  @apply bg-gradient-to-br from-primary-purple/10 to-primary-blue/10 border-2 border-primary-purple;
}

/* Form styles */
.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-purple focus:border-transparent transition-all duration-150;
}

.form-textarea {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-purple focus:border-transparent transition-all duration-150 resize-none;
}

/* Card styles */
.card {
  @apply bg-white rounded-2xl shadow-soft p-6 transition-all duration-150 hover:shadow-lg;
}