import { useEffect, useState } from 'react'

/**
 * Custom hook for debouncing values
 * @param value - The value to debounce
 * @param delay - The delay in milliseconds
 * @returns The debounced value
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

/**
 * Custom hook for debounced auto-save functionality
 * @param value - The value to auto-save
 * @param onSave - The function to call when saving
 * @param delay - The delay in milliseconds (default: 1000ms)
 * @param enabled - Whether auto-save is enabled (default: true)
 */
export function useAutoSave<T>(
  value: T,
  onSave: (value: T) => Promise<void> | void,
  delay: number = 1000,
  enabled: boolean = true
) {
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<T>(value)
  const debouncedValue = useDebounce(value, delay)

  useEffect(() => {
    if (!enabled) return
    
    // Don't save if the value hasn't changed
    if (JSON.stringify(debouncedValue) === JSON.stringify(lastSaved)) {
      return
    }

    const save = async () => {
      setIsSaving(true)
      try {
        await onSave(debouncedValue)
        setLastSaved(debouncedValue)
      } catch (error) {
        console.error('Auto-save failed:', error)
      } finally {
        setIsSaving(false)
      }
    }

    save()
  }, [debouncedValue, onSave, enabled, lastSaved])

  return { isSaving, lastSaved }
}
