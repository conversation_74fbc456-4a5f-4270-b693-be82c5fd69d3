import { createClient } from '@/lib/supabase/client'
import { Database, SubscriptionTier, SubscriptionStatus } from '@/lib/supabase/database.types'

type Subscription = Database['public']['Tables']['subscriptions']['Row']
type SubscriptionInsert = Database['public']['Tables']['subscriptions']['Insert']
type SubscriptionUpdate = Database['public']['Tables']['subscriptions']['Update']

export class SubscriptionService {
  private static supabase = createClient()

  /**
   * Get user's current subscription
   */
  static async getCurrentSubscription(userId: string): Promise<Subscription | null> {
    try {
      const { data, error } = await this.supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (error && error.code !== 'PGRST116') {
        throw error
      }

      return data
    } catch (error) {
      console.error('Error fetching subscription:', error)
      return null
    }
  }

  /**
   * Get user's subscription history
   */
  static async getSubscriptionHistory(userId: string): Promise<Subscription[]> {
    try {
      const { data, error } = await this.supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching subscription history:', error)
      return []
    }
  }

  /**
   * Create a new subscription
   */
  static async createSubscription(subscription: SubscriptionInsert): Promise<Subscription | null> {
    try {
      const { data, error } = await this.supabase
        .from('subscriptions')
        .insert(subscription)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating subscription:', error)
      return null
    }
  }

  /**
   * Update subscription
   */
  static async updateSubscription(
    subscriptionId: string, 
    updates: SubscriptionUpdate
  ): Promise<Subscription | null> {
    try {
      const { data, error } = await this.supabase
        .from('subscriptions')
        .update(updates)
        .eq('id', subscriptionId)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating subscription:', error)
      return null
    }
  }

  /**
   * Cancel subscription
   */
  static async cancelSubscription(subscriptionId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('subscriptions')
        .update({
          status: 'canceled',
          canceled_at: new Date().toISOString(),
          cancel_at_period_end: true
        })
        .eq('id', subscriptionId)

      if (error) throw error
      return true
    } catch (error) {
      console.error('Error canceling subscription:', error)
      return false
    }
  }

  /**
   * Check if user has access to feature based on subscription tier
   */
  static async hasFeatureAccess(
    userId: string, 
    feature: string
  ): Promise<boolean> {
    try {
      const subscription = await this.getCurrentSubscription(userId)
      if (!subscription) return false

      const featureAccess = {
        free: [
          'basic_content_generation',
          'single_brand',
          'basic_templates'
        ],
        starter: [
          'basic_content_generation',
          'multiple_brands',
          'basic_templates',
          'advanced_content_generation',
          'analytics_basic'
        ],
        professional: [
          'basic_content_generation',
          'multiple_brands',
          'basic_templates',
          'advanced_content_generation',
          'analytics_basic',
          'analytics_advanced',
          'team_collaboration',
          'api_access',
          'custom_templates'
        ],
        enterprise: [
          'basic_content_generation',
          'multiple_brands',
          'basic_templates',
          'advanced_content_generation',
          'analytics_basic',
          'analytics_advanced',
          'team_collaboration',
          'api_access',
          'custom_templates',
          'white_label',
          'priority_support',
          'custom_integrations'
        ]
      }

      const tierFeatures = featureAccess[subscription.tier] || []
      return tierFeatures.includes(feature)
    } catch (error) {
      console.error('Error checking feature access:', error)
      return false
    }
  }

  /**
   * Get subscription tier limits
   */
  static getSubscriptionLimits(tier: SubscriptionTier) {
    const limits = {
      free: {
        content_generation: 50,
        brands: 1,
        team_members: 1,
        api_calls: 100,
        storage_mb: 1000,
        analytics_retention_days: 30
      },
      starter: {
        content_generation: 500,
        brands: 3,
        team_members: 3,
        api_calls: 1000,
        storage_mb: 10000,
        analytics_retention_days: 90
      },
      professional: {
        content_generation: 2000,
        brands: 10,
        team_members: 10,
        api_calls: 5000,
        storage_mb: 50000,
        analytics_retention_days: 365
      },
      enterprise: {
        content_generation: -1, // Unlimited
        brands: -1,
        team_members: -1,
        api_calls: -1,
        storage_mb: -1,
        analytics_retention_days: -1
      }
    }

    return limits[tier] || limits.free
  }

  /**
   * Check if subscription is active
   */
  static isSubscriptionActive(subscription: Subscription): boolean {
    if (!subscription) return false
    
    const now = new Date()
    const currentPeriodEnd = new Date(subscription.current_period_end || '')
    
    return (
      subscription.status === 'active' || 
      (subscription.status === 'trialing' && currentPeriodEnd > now)
    )
  }

  /**
   * Get days remaining in trial
   */
  static getTrialDaysRemaining(subscription: Subscription): number {
    if (!subscription.trial_end) return 0
    
    const now = new Date()
    const trialEnd = new Date(subscription.trial_end)
    const diffTime = trialEnd.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return Math.max(0, diffDays)
  }

  /**
   * Get subscription status display text
   */
  static getStatusDisplayText(subscription: Subscription): string {
    switch (subscription.status) {
      case 'active':
        return 'Active'
      case 'trialing':
        const daysRemaining = this.getTrialDaysRemaining(subscription)
        return `Trial (${daysRemaining} days remaining)`
      case 'canceled':
        return 'Canceled'
      case 'past_due':
        return 'Past Due'
      case 'unpaid':
        return 'Unpaid'
      default:
        return 'Unknown'
    }
  }

  /**
   * Get tier display information
   */
  static getTierDisplayInfo(tier: SubscriptionTier) {
    const tierInfo = {
      free: {
        name: 'Free',
        price: '$0',
        description: 'Perfect for getting started',
        features: [
          '50 content generations/month',
          '1 brand',
          'Basic templates',
          'Community support'
        ]
      },
      starter: {
        name: 'Starter',
        price: '$29',
        description: 'Great for small businesses',
        features: [
          '500 content generations/month',
          '3 brands',
          'Advanced templates',
          'Basic analytics',
          'Email support'
        ]
      },
      professional: {
        name: 'Professional',
        price: '$99',
        description: 'Perfect for growing teams',
        features: [
          '2,000 content generations/month',
          '10 brands',
          'Team collaboration',
          'Advanced analytics',
          'API access',
          'Priority support'
        ]
      },
      enterprise: {
        name: 'Enterprise',
        price: 'Custom',
        description: 'For large organizations',
        features: [
          'Unlimited content generations',
          'Unlimited brands',
          'White-label solution',
          'Custom integrations',
          'Dedicated support',
          'SLA guarantee'
        ]
      }
    }

    return tierInfo[tier] || tierInfo.free
  }
}
