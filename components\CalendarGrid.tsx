'use client'

import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isToday } from 'date-fns'
import { Clock, Edit3, Instagram, Linkedin, Twitter, CheckSquare, Square } from 'lucide-react'
import { ContentItem } from '@/lib/types/content'

interface CalendarGridProps {
  content: ContentItem[]
  currentMonth: Date
  onContentClick: (content: ContentItem) => void
  bulkSelectMode?: boolean
  selectedItems?: string[]
  onItemSelect?: (itemId: string, selected: boolean) => void
}

const platformIcons = {
  instagram: <Instagram className="w-4 h-4" />,
  tiktok: <div className="w-4 h-4 bg-black rounded-full" />,
  linkedin: <Linkedin className="w-4 h-4" />,
  twitter: <Twitter className="w-4 h-4" />
}

const platformColors = {
  instagram: 'from-pink-500 to-purple-500',
  tiktok: 'from-black to-gray-800',
  linkedin: 'from-blue-600 to-blue-700',
  twitter: 'from-gray-800 to-black'
}

export default function CalendarGrid({
  content,
  currentMonth,
  onContentClick,
  bulkSelectMode = false,
  selectedItems = [],
  onItemSelect
}: CalendarGridProps) {
  const monthStart = startOfMonth(currentMonth)
  const monthEnd = endOfMonth(currentMonth)
  const days = eachDayOfInterval({ start: monthStart, end: monthEnd })

  // Get the first day of the week for the month (0 = Sunday, 1 = Monday, etc.)
  const firstDayOfWeek = monthStart.getDay()

  // Create empty cells for days before the month starts
  const emptyCells = Array.from({ length: firstDayOfWeek }, (_, i) => i)

  const getContentForDate = (date: Date) => {
    return content.filter(item => isSameDay(new Date(item.date), date))
  }

  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

  return (
    <div className="bg-white rounded-lg overflow-hidden">
      {/* Week day headers */}
      <div className="grid grid-cols-7 bg-gray-50">
        {weekDays.map((day) => (
          <div key={day} className="p-3 text-center text-sm font-medium text-gray-600">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar grid */}
      <div className="grid grid-cols-7">
        {/* Empty cells for days before month starts */}
        {emptyCells.map((_, index) => (
          <div key={`empty-${index}`} className="calendar-cell bg-gray-50" />
        ))}

        {/* Days of the month */}
        {days.map((day) => {
          const dayContent = getContentForDate(day)
          const isCurrentDay = isToday(day)

          return (
            <div
              key={day.toISOString()}
              className={`calendar-cell relative ${isCurrentDay ? 'bg-blue-50 border-blue-200' : ''
                } ${dayContent.length > 0 ? 'hover:shadow-md' : ''
                }`}
            >
              {/* Date number */}
              <div className={`text-sm font-medium mb-2 ${isCurrentDay ? 'text-blue-600' : 'text-gray-700'
                }`}>
                {format(day, 'd')}
              </div>

              {/* Content items */}
              <div className="space-y-1">
                {dayContent.slice(0, 3).map((item) => (
                  <div key={item.id} className="relative">
                    {bulkSelectMode && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          onItemSelect?.(item.id, !selectedItems.includes(item.id))
                        }}
                        className="absolute top-1 right-1 z-10 p-1 bg-white/90 rounded hover:bg-white transition-colors"
                      >
                        {selectedItems.includes(item.id) ? (
                          <CheckSquare className="w-3 h-3 text-primary-purple" />
                        ) : (
                          <Square className="w-3 h-3 text-gray-400" />
                        )}
                      </button>
                    )}
                    <button
                      onClick={() => bulkSelectMode ? onItemSelect?.(item.id, !selectedItems.includes(item.id)) : onContentClick(item)}
                      className="w-full text-left group"
                    >
                      <div className={`p-2 rounded-md bg-gradient-to-r ${platformColors[item.platform as keyof typeof platformColors]} text-white text-xs transition-all duration-150 group-hover:scale-105 ${bulkSelectMode && selectedItems.includes(item.id) ? 'ring-2 ring-primary-purple ring-offset-1' : ''}`}>
                        <div className="flex items-center gap-1 mb-1">
                          {platformIcons[item.platform as keyof typeof platformIcons]}
                          <span className="font-medium capitalize">
                            {item.platform}
                          </span>
                        </div>
                        <div className="truncate opacity-90">
                          {item.caption.substring(0, 30)}...
                        </div>
                        <div className="flex items-center gap-1 mt-1 opacity-75">
                          <Clock className="w-3 h-3" />
                          <span className="text-xs">{item.bestTime}</span>
                        </div>
                      </div>
                    </button>
                  </div>
                ))}

                {/* Show more indicator */}
                {dayContent.length > 3 && (
                  <div className="text-xs text-gray-500 text-center py-1">
                    +{dayContent.length - 3} more
                  </div>
                )}

                {/* Empty state for days with no content */}
                {dayContent.length === 0 && (
                  <div className="h-16 flex items-center justify-center text-gray-300">
                    <div className="text-xs opacity-50">No posts</div>
                  </div>
                )}
              </div>

              {/* Edit indicator */}
              {dayContent.length > 0 && (
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-150">
                  <Edit3 className="w-3 h-3 text-gray-400" />
                </div>
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}