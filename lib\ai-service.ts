import OpenAI from 'openai'
import { format, eachDayOfInterval, startOfMonth, endOfMonth } from 'date-fns'
import { v4 as uuidv4 } from 'uuid'
import { ContentItem } from '@/lib/types/content'
import { ModelSettingsService } from '@/lib/services/modelSettingsService'
import { GeminiService } from '@/lib/services/geminiService'
import { ViralPromptService } from '@/lib/services/viralPromptService'
import { ModelSwitcher } from '@/lib/utils/modelSwitcher'

interface BrandData {
  brandName: string
  industry: string
  tone: string
  targetAudience: string
  platforms: string[]
}

// Initialize OpenAI client with OpenRouter
const openai = new OpenAI({
  apiKey: '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',
  baseURL: 'https://openrouter.ai/api/v1',
  dangerouslyAllowBrowser: true // Note: In production, API calls should be made from the backend
})

// Platform name mapping from display names to internal names
const platformNameMapping: Record<string, string> = {
  'Instagram': 'instagram',
  'TikTok': 'tiktok',
  'LinkedIn': 'linkedin',
  'X/Twitter': 'twitter',
  'Twitter': 'twitter',
  'Facebook': 'instagram', // Use instagram config as fallback
  'YouTube': 'instagram', // Use instagram config as fallback
  'Pinterest': 'instagram' // Use instagram config as fallback
}

// Function to clean and parse AI JSON response
function parseAIResponse(aiResponse: string): any {
  try {
    // Remove markdown code blocks if present
    let cleanedResponse = aiResponse.trim()

    // Remove ```json and ``` markers
    if (cleanedResponse.startsWith('```json')) {
      cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '')
    } else if (cleanedResponse.startsWith('```')) {
      cleanedResponse = cleanedResponse.replace(/^```\s*/, '').replace(/\s*```$/, '')
    }

    // Try to parse the cleaned response
    return JSON.parse(cleanedResponse.trim())
  } catch (error) {
    console.error('❌ Failed to parse AI response:', error)
    console.error('Raw response:', aiResponse)

    // Try to extract JSON from the response using regex
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/)
    if (jsonMatch) {
      try {
        return JSON.parse(jsonMatch[0])
      } catch (regexError) {
        console.error('❌ Failed to parse extracted JSON:', regexError)
      }
    }

    // If all parsing fails, return a fallback structure
    console.warn('⚠️ Using fallback JSON structure due to parsing failure')
    return {
      caption: "🚀 Exciting content coming soon! Stay tuned for updates.",
      hashtags: ["#content", "#comingsoon", "#exciting"],
      visualPrompt: "Create an engaging social media post with modern, clean design"
    }
  }
}

// Enhanced fallback content generators for rate limiting scenarios
function generateEnhancedFallbackCaption(brandData: BrandData, platform: string, theme: string): string {
  const templates = {
    educational: [
      `💡 Pro tip from ${brandData.brandName}: Success in ${brandData.industry} starts with understanding your audience. What's your biggest challenge? Let's discuss! 🧵`,
      `🎯 ${brandData.brandName} insight: The key to ${brandData.industry} excellence is consistency and innovation. Here's what we've learned... 📈`,
      `📚 Quick lesson from ${brandData.brandName}: In ${brandData.industry}, staying ahead means never stopping learning. What's your next goal? 🚀`
    ],
    promotional: [
      `🚀 Exciting news from ${brandData.brandName}! We're bringing you something special in ${brandData.industry}. Stay tuned for the big reveal! ✨`,
      `✨ ${brandData.brandName} is proud to serve ${brandData.targetAudience} with cutting-edge ${brandData.industry} solutions. Ready to level up? 💪`,
      `🎉 Big things are happening at ${brandData.brandName}! Our ${brandData.industry} expertise is about to change the game. Are you ready? 🔥`
    ],
    inspirational: [
      `💪 Monday motivation from ${brandData.brandName}: Every expert in ${brandData.industry} was once a beginner who refused to give up. Keep pushing! 🌟`,
      `🌟 ${brandData.brandName} believes in the power of ${brandData.targetAudience} to transform ${brandData.industry}. What's your vision? ✨`,
      `🚀 Dream big, start small, act now. That's the ${brandData.brandName} way in ${brandData.industry}. What's your next move? 💫`
    ]
  }

  const themeTemplates = templates[theme as keyof typeof templates] || templates.educational
  return themeTemplates[Math.floor(Math.random() * themeTemplates.length)]
}

function generateEnhancedFallbackHashtags(brandData: BrandData, platform: string): string[] {
  const baseHashtags = [`#${brandData.industry}`, `#${brandData.brandName.replace(/\s+/g, '')}`]

  const platformHashtags = {
    instagram: ['#business', '#growth', '#success', '#motivation', '#entrepreneur', '#innovation'],
    tiktok: ['#tips', '#business', '#growth', '#success'],
    linkedin: ['#business', '#industry', '#growth', '#professional', '#leadership'],
    twitter: ['#business', '#tips', '#growth', '#success']
  }

  const normalizedPlatform = normalizePlatformName(platform)
  const platformSpecific = platformHashtags[normalizedPlatform as keyof typeof platformHashtags] || platformHashtags.instagram

  return [...baseHashtags, ...platformSpecific.slice(0, 3)]
}

function generateEnhancedFallbackVisual(brandData: BrandData, platform: string, contentType: string): string {
  return `Create a professional ${contentType} for ${brandData.brandName} in the ${brandData.industry} industry. Use modern, clean design with brand colors. Include relevant visual elements that represent ${brandData.industry} and appeal to ${brandData.targetAudience}. Optimize for ${platform} format and best practices.`
}

// Function to normalize platform names
function normalizePlatformName(platform: string): string {
  // First try direct mapping
  if (platformNameMapping[platform]) {
    return platformNameMapping[platform]
  }

  // Then try lowercase
  const lowerPlatform = platform.toLowerCase()
  if (platformConfig[lowerPlatform as keyof typeof platformConfig]) {
    return lowerPlatform
  }

  // Default fallback to instagram
  console.warn(`Unknown platform: ${platform}, falling back to instagram`)
  return 'instagram'
}

// Platform-specific content types and optimal posting times
const platformConfig = {
  instagram: {
    types: ['photo', 'carousel', 'reel', 'story'],
    optimalTimes: ['09:00', '11:00', '13:00', '15:00', '17:00', '19:00'],
    hashtagCount: { min: 5, max: 10 }
  },
  tiktok: {
    types: ['video', 'trend', 'challenge', 'educational'],
    optimalTimes: ['06:00', '10:00', '19:00', '20:00', '21:00'],
    hashtagCount: { min: 3, max: 5 }
  },
  linkedin: {
    types: ['article', 'post', 'poll', 'document'],
    optimalTimes: ['08:00', '09:00', '12:00', '13:00', '17:00'],
    hashtagCount: { min: 3, max: 5 }
  },
  twitter: {
    types: ['tweet', 'thread', 'poll', 'quote'],
    optimalTimes: ['09:00', '12:00', '15:00', '18:00', '21:00'],
    hashtagCount: { min: 1, max: 3 }
  }
}

// Enhanced content themes with weights for better distribution - optimized for virality
const contentThemes = {
  educational: { weight: 30, variations: ['how-to', 'tutorial', 'tips', 'guide', 'explanation', 'myth-busting'] },
  promotional: { weight: 15, variations: ['product-showcase', 'feature-highlight', 'offer', 'announcement', 'launch'] },
  'behind-the-scenes': { weight: 20, variations: ['process', 'team', 'workspace', 'culture', 'day-in-life'] },
  inspirational: { weight: 15, variations: ['motivation', 'success-story', 'quote', 'achievement', 'transformation'] },
  community: { weight: 10, variations: ['user-generated', 'testimonial', 'collaboration', 'engagement', 'qa'] },
  trending: { weight: 15, variations: ['current-events', 'viral-topic', 'meme', 'challenge', 'trend-analysis'] },
  seasonal: { weight: 5, variations: ['holiday', 'weather', 'events', 'calendar'] }
}

// Industry-specific content templates
const industryTemplates = {
  'E-commerce': {
    educational: [
      'Product comparison guides',
      'Styling tips and tricks',
      'Care and maintenance guides',
      'Size and fit guides'
    ],
    promotional: [
      'New arrival showcases',
      'Limited time offers',
      'Bundle deals',
      'Customer favorites'
    ],
    'behind-the-scenes': [
      'Product sourcing stories',
      'Quality control process',
      'Packaging and shipping',
      'Team picks and favorites'
    ]
  },
  'Technology': {
    educational: [
      'Feature deep dives',
      'Best practices and workflows',
      'Industry trend analysis',
      'Problem-solving tutorials'
    ],
    promotional: [
      'Product updates and releases',
      'Integration showcases',
      'Performance benchmarks',
      'Customer success metrics'
    ],
    'behind-the-scenes': [
      'Development process insights',
      'Team collaboration tools',
      'Innovation and R&D',
      'Company culture and values'
    ]
  },
  'Health & Wellness': {
    educational: [
      'Wellness tips and advice',
      'Myth-busting content',
      'Ingredient spotlights',
      'Routine and habit building'
    ],
    promotional: [
      'Product benefits and results',
      'Before and after stories',
      'Expert endorsements',
      'Community challenges'
    ],
    'behind-the-scenes': [
      'Research and development',
      'Quality and safety testing',
      'Expert partnerships',
      'Mission and values'
    ]
  },
  'Food & Beverage': {
    educational: [
      'Recipe tutorials and tips',
      'Ingredient education',
      'Nutrition and health benefits',
      'Cooking techniques'
    ],
    promotional: [
      'Seasonal menu items',
      'Chef specials',
      'Pairing suggestions',
      'Limited edition flavors'
    ],
    'behind-the-scenes': [
      'Kitchen and preparation',
      'Sourcing and ingredients',
      'Chef and staff stories',
      'Restaurant atmosphere'
    ]
  }
}

// Helper function to select theme based on weights
function selectWeightedTheme(): string {
  const themes = Object.keys(contentThemes)
  const weights = themes.map(theme => contentThemes[theme as keyof typeof contentThemes].weight)
  const totalWeight = weights.reduce((sum, weight) => sum + weight, 0)

  let random = Math.random() * totalWeight
  for (let i = 0; i < themes.length; i++) {
    random -= weights[i]
    if (random <= 0) {
      return themes[i]
    }
  }
  return themes[0] // fallback
}

// Helper function to get industry-specific content ideas
function getIndustrySpecificContent(industry: string, theme: string): string[] {
  const industryKey = industry as keyof typeof industryTemplates
  const industryTemplate = industryTemplates[industryKey]

  if (industryTemplate && industryTemplate[theme as keyof typeof industryTemplate]) {
    return industryTemplate[theme as keyof typeof industryTemplate]
  }

  // Fallback to generic content
  const themeData = contentThemes[theme as keyof typeof contentThemes]
  return themeData ? themeData.variations : ['general content']
}

// Helper function to get platform-specific engagement tactics
function getPlatformEngagementTactics(platform: string): string[] {
  const tactics = {
    instagram: [
      'Ask a question in the caption',
      'Use Instagram Stories polls',
      'Create carousel posts for higher engagement',
      'Use trending audio for Reels',
      'Partner with micro-influencers'
    ],
    tiktok: [
      'Jump on trending sounds and hashtags',
      'Create educational content in short format',
      'Use trending effects and filters',
      'Collaborate with other creators',
      'Post consistently at peak times'
    ],
    linkedin: [
      'Share industry insights and data',
      'Ask thought-provoking questions',
      'Share behind-the-scenes professional content',
      'Engage with comments promptly',
      'Use LinkedIn polls for engagement'
    ],
    twitter: [
      'Join trending conversations',
      'Create Twitter threads for complex topics',
      'Use relevant hashtags strategically',
      'Retweet and comment on industry leaders',
      'Share quick tips and insights'
    ]
  }

  return tactics[platform as keyof typeof tactics] || []
}

// Helper function to get tone-specific guidelines
function getToneGuidelines(tone: string): string {
  const guidelines = {
    professional: 'Use formal language, industry terminology, and authoritative voice. Focus on expertise and credibility.',
    casual: 'Use conversational language, contractions, and friendly tone. Be approachable and relatable.',
    playful: 'Use humor, emojis, and creative language. Be fun and entertaining while staying on-brand.',
    inspirational: 'Use motivational language, positive messaging, and uplifting tone. Inspire action and positivity.',
    educational: 'Use clear explanations, step-by-step guidance, and informative tone. Focus on teaching and helping.',
    luxury: 'Use sophisticated language, exclusive terminology, and premium tone. Emphasize quality and exclusivity.',
    friendly: 'Use warm, welcoming language with personal touches. Be approachable and community-focused.',
    authoritative: 'Use confident, expert language with data-backed claims. Establish thought leadership.',
    humorous: 'Use wit, clever wordplay, and light humor. Keep it appropriate and brand-aligned.',
    empathetic: 'Use understanding, supportive language that acknowledges audience challenges and emotions.'
  }

  return guidelines[tone.toLowerCase() as keyof typeof guidelines] ||
    'Maintain a consistent brand voice that resonates with your target audience.'
}

export async function generateCalendarContent(
  brandData: BrandData,
  month: Date
): Promise<ContentItem[]> {
  const monthStart = startOfMonth(month)
  const monthEnd = endOfMonth(month)
  const days = eachDayOfInterval({ start: monthStart, end: monthEnd })

  const content: ContentItem[] = []
  let rateLimitHit = false

  // Generate content for each day
  for (const day of days) {
    // Skip weekends for LinkedIn (business platform)
    const isWeekend = day.getDay() === 0 || day.getDay() === 6
    const platformsForDay = isWeekend
      ? brandData.platforms.filter(p => p !== 'linkedin')
      : brandData.platforms

    // Randomly select 1-2 platforms per day to avoid overwhelming
    const selectedPlatforms = platformsForDay
      .sort(() => Math.random() - 0.5)
      .slice(0, Math.random() > 0.7 ? 2 : 1)

    for (const platform of selectedPlatforms) {
      try {
        // If we've hit rate limits, skip AI calls and use fallback
        if (rateLimitHit) {
          console.log('⚠️ Skipping AI call due to previous rate limit, using fallback')
          content.push(createFallbackContent(brandData, day, platform))
          continue
        }

        const contentItem = await generateSinglePost(
          brandData,
          day,
          platform
        )
        content.push(contentItem)

        // Add small delay between requests to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 200))

      } catch (error) {
        console.error(`Failed to generate content for ${platform} on ${format(day, 'yyyy-MM-dd')}:`, error)

        // Check if it's a rate limit error
        if (error instanceof Error && (error.message.includes('429') || error.message.includes('Rate limit'))) {
          console.warn('🚫 Rate limit detected, switching to fallback mode for remaining content')
          rateLimitHit = true
        }

        // Create fallback content
        content.push(createFallbackContent(brandData, day, platform))
      }
    }
  }

  return content.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
}

export async function generateSinglePost(
  brandData: BrandData,
  date: Date,
  platform: string
): Promise<ContentItem> {
  console.log('🎯 generateSinglePost called with platform:', platform)

  // Normalize platform name using the mapping function
  const normalizedPlatform = normalizePlatformName(platform)
  console.log('🔄 Normalized platform:', normalizedPlatform)

  // Get platform config (should always exist now due to fallback)
  const config = platformConfig[normalizedPlatform as keyof typeof platformConfig]

  const contentType = config.types[Math.floor(Math.random() * config.types.length)]
  const theme = selectWeightedTheme()
  const themeVariation = contentThemes[theme as keyof typeof contentThemes].variations[
    Math.floor(Math.random() * contentThemes[theme as keyof typeof contentThemes].variations.length)
  ]
  const bestTime = config.optimalTimes[Math.floor(Math.random() * config.optimalTimes.length)]
  const industryContent = getIndustrySpecificContent(brandData.industry, theme)
  const engagementTactics = getPlatformEngagementTactics(platform)
  const specificContentIdea = industryContent[Math.floor(Math.random() * industryContent.length)]
  const engagementTactic = engagementTactics[Math.floor(Math.random() * engagementTactics.length)]

  // Create viral prompt configuration
  const viralConfig = {
    brand: {
      name: brandData.brandName,
      industry: brandData.industry,
      tone: brandData.tone,
      targetAudience: brandData.targetAudience
    },
    content: {
      type: contentType,
      theme: theme,
      variation: themeVariation,
      specificIdea: specificContentIdea,
      date: format(date, 'EEEE, MMMM do, yyyy')
    },
    platform: {
      name: platform,
      engagementTactic: engagementTactic,
      hashtagCount: config.hashtagCount
    }
  }

  // Generate viral-optimized prompts using the framework
  const systemPrompt = ViralPromptService.generateViralSystemPrompt(brandData, platform, 'viral')
  const userPrompt = ViralPromptService.generateViralUserPrompt(contentType, theme, 9, platform)

  // Get current model from settings - prioritize Gemini 2.5 models
  let currentModel = ModelSwitcher.getBestAvailableModel()

  // Override to use latest Gemini models if available (prioritize 2.0 Flash for speed and quality)
  const preferredModels = ['gemini-2.0-flash', 'gemini-2.5-flash', 'gemini-2.5-pro', 'gemini-1.5-pro']
  for (const model of preferredModels) {
    if (await ModelSettingsService.validateModel(model)) {
      currentModel = model
      break
    }
  }

  let modelSettings = ModelSettingsService.getRecommendedSettings(currentModel)

  try {

    // Check if it's a Gemini model
    if (ModelSettingsService.isGeminiModel(currentModel)) {
      console.log(`🤖 Calling Google Gemini API with ${currentModel}...`)

      const geminiPrompt = GeminiService.formatPromptForGemini(systemPrompt, userPrompt)
      const aiResponse = await GeminiService.generateContent(currentModel, geminiPrompt, modelSettings)

      console.log('🔍 Raw Gemini response:', aiResponse.substring(0, 200) + '...')

      // Parse Gemini response with robust error handling
      const parsedContent = parseAIResponse(aiResponse)

      // Validate required fields
      if (!parsedContent.caption || !parsedContent.hashtags || !parsedContent.visualPrompt) {
        console.warn('⚠️ Gemini response missing required fields, using fallback')
        throw new Error('Invalid Gemini response structure')
      }

      return {
        id: uuidv4(),
        date: format(date, 'yyyy-MM-dd'),
        platform,
        type: contentType,
        caption: parsedContent.caption || "🚀 Exciting content coming soon!",
        hashtags: Array.isArray(parsedContent.hashtags)
          ? parsedContent.hashtags.map((tag: string) =>
            tag.startsWith('#') ? tag : `#${tag}`
          )
          : ['#content', '#social'],
        visualPrompt: parsedContent.visualPrompt || "Create an engaging social media post",
        status: 'draft' as const,
        engagement: { likes: 0, comments: 0, shares: 0 },
        bestTime
      }
    }

    // OpenRouter models
    console.log(`🤖 Calling OpenRouter API with ${currentModel}...`)

    const response = await openai.chat.completions.create({
      model: currentModel,
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: userPrompt
        }
      ],
      temperature: modelSettings.temperature,
      max_tokens: modelSettings.max_tokens,
      top_p: modelSettings.top_p
    })

    console.log('✅ OpenRouter API call successful')

    const aiResponse = response.choices[0]?.message?.content
    if (!aiResponse) {
      throw new Error('No response from AI')
    }

    console.log('🔍 Raw AI response:', aiResponse.substring(0, 200) + '...')

    // Parse AI response with robust error handling
    const parsedContent = parseAIResponse(aiResponse)

    // Validate required fields
    if (!parsedContent.caption || !parsedContent.hashtags || !parsedContent.visualPrompt) {
      console.warn('⚠️ AI response missing required fields, using fallback')
      throw new Error('Invalid AI response structure')
    }

    return {
      id: uuidv4(),
      date: format(date, 'yyyy-MM-dd'),
      platform,
      type: contentType,
      caption: parsedContent.caption || "🚀 Exciting content coming soon!",
      hashtags: Array.isArray(parsedContent.hashtags)
        ? parsedContent.hashtags.map((tag: string) =>
          tag.startsWith('#') ? tag : `#${tag}`
        )
        : ['#content', '#social'],
      visualPrompt: parsedContent.visualPrompt || "Create an engaging social media post",
      status: 'draft' as const,
      engagement: { likes: 0, comments: 0, shares: 0 },
      bestTime
    }
  } catch (error) {
    console.error('AI generation error:', error)

    // Handle different types of API errors
    if (error instanceof Error) {
      // Try to handle rate limiting with automatic model switching
      try {
        const newModel = ModelSwitcher.handleRateLimitError(error, currentModel)

        // If we got a new model, throw a user-friendly error asking to retry
        if (newModel !== currentModel) {
          const switchError = new Error(`Rate limit exceeded for ${ModelSettingsService.getModelInfo(currentModel).name}. Switched to ${ModelSettingsService.getModelInfo(newModel).name}. Please try generating again.`)
          switchError.name = 'ModelSwitchedError'
          throw switchError
        }
      } catch (switchError) {
        // If ModelSwitcher threw an error, it means it's a rate limit we should handle
        if (switchError instanceof Error && switchError.message.includes('rate limit')) {
          throw switchError
        }
        // Otherwise, continue with original error handling
      }

      // Handle model not found errors (404)
      if (error.message.includes('404') || error.message.includes('No endpoints found')) {
        console.warn('⚠️ Selected model not available, switching to default model')

        // Switch to default model and retry
        const defaultModel = 'openai/gpt-oss-20b:free'
        ModelSettingsService.updateSelectedModel(defaultModel)

        const fallbackError = new Error(`Selected model not available. Switched to ${ModelSettingsService.getModelInfo(defaultModel).name}. Please try again.`)
        fallbackError.name = 'ModelNotFoundError'
        throw fallbackError
      }

      // Handle other API errors
      if (error.message.includes('API error')) {
        console.warn('⚠️ API error, using enhanced fallback content')
      }
    }

    // Return enhanced fallback content instead of throwing
    return {
      id: uuidv4(),
      date: format(date, 'yyyy-MM-dd'),
      platform,
      type: contentType,
      caption: generateEnhancedFallbackCaption(brandData, platform, theme),
      hashtags: generateEnhancedFallbackHashtags(brandData, platform),
      visualPrompt: generateEnhancedFallbackVisual(brandData, platform, contentType),
      status: 'draft' as const,
      engagement: { likes: 0, comments: 0, shares: 0 },
      bestTime
    }
  }
}

function createFallbackContent(
  brandData: BrandData,
  date: Date,
  platform: string
): ContentItem {
  console.log('🔄 createFallbackContent called with platform:', platform)

  // Normalize platform name using the mapping function
  const normalizedPlatform = normalizePlatformName(platform)
  console.log('🔄 Normalized platform in fallback:', normalizedPlatform)

  // Get platform config (should always exist now due to fallback)
  const config = platformConfig[normalizedPlatform as keyof typeof platformConfig]
  const contentType = config.types[0]
  const bestTime = config.optimalTimes[0]

  const fallbackCaptions = {
    instagram: `✨ Another amazing day at ${brandData.brandName}! We're passionate about delivering excellence in ${brandData.industry}. What's your favorite thing about our brand? Let us know in the comments! 💬`,
    tiktok: `🔥 Quick tip from ${brandData.brandName}! Here's something every ${brandData.targetAudience} should know about ${brandData.industry}. Save this for later! 📌`,
    linkedin: `Insights from ${brandData.brandName}: The ${brandData.industry} landscape is constantly evolving. Here's what we're seeing and how it impacts ${brandData.targetAudience}. What are your thoughts?`,
    twitter: `💡 ${brandData.brandName} tip: Success in ${brandData.industry} comes from understanding your audience. What's your biggest challenge? Let's discuss! 🧵`
  }

  const fallbackHashtags = {
    instagram: ['#business', '#growth', '#success', '#motivation', '#entrepreneur'],
    tiktok: ['#tips', '#business', '#growth'],
    linkedin: ['#business', '#industry', '#growth', '#professional'],
    twitter: ['#business', '#tips']
  }

  return {
    id: uuidv4(),
    date: format(date, 'yyyy-MM-dd'),
    platform,
    type: contentType,
    caption: fallbackCaptions[normalizedPlatform as keyof typeof fallbackCaptions],
    hashtags: fallbackHashtags[normalizedPlatform as keyof typeof fallbackHashtags],
    visualPrompt: `Professional ${brandData.industry} themed image featuring ${brandData.brandName} branding. Clean, modern design with brand colors. Include relevant visual elements that represent ${brandData.industry} and appeal to ${brandData.targetAudience}.`,
    status: 'draft' as const,
    engagement: { likes: 0, comments: 0, shares: 0 },
    bestTime
  }
}

// Generate multiple content variations for A/B testing
export async function generateContentVariations(
  brandData: BrandData,
  date: Date,
  platform: string,
  variationCount: number = 3
): Promise<ContentItem[]> {
  const variations: ContentItem[] = []

  for (let i = 0; i < variationCount; i++) {
    try {
      const variation = await generateSinglePost(brandData, date, platform)
      variation.id = `${variation.id}-v${i + 1}`
      variations.push(variation)
    } catch (error) {
      console.error(`Failed to generate variation ${i + 1}:`, error)
    }
  }

  return variations
}

// Get content performance predictions based on content analysis
export function analyzeContentPotential(content: ContentItem): {
  engagementScore: number
  viralPotential: number
  brandAlignment: number
  recommendations: string[]
} {
  const caption = content.caption.toLowerCase()
  const hashtags = content.hashtags.join(' ').toLowerCase()

  // Engagement score factors
  let engagementScore = 50 // base score

  // Question in caption boosts engagement
  if (caption.includes('?')) engagementScore += 15

  // Call-to-action words
  const ctaWords = ['comment', 'share', 'tag', 'follow', 'like', 'save', 'try', 'click']
  const ctaCount = ctaWords.filter(word => caption.includes(word)).length
  engagementScore += ctaCount * 5

  // Emoji usage (moderate is good)
  const emojiCount = (caption.match(/[\u1F600-\u1F64F]|[\u1F300-\u1F5FF]|[\u1F680-\u1F6FF]|[\u1F1E0-\u1F1FF]/g) || []).length
  if (emojiCount >= 1 && emojiCount <= 5) engagementScore += 10

  // Viral potential factors
  let viralPotential = 30 // base score

  // Trending topics and hashtags
  const trendingWords = ['trending', 'viral', 'challenge', 'new', 'exclusive', 'limited']
  const trendingCount = trendingWords.filter(word => caption.includes(word) || hashtags.includes(word)).length
  viralPotential += trendingCount * 10

  // Controversy or strong opinions (moderate boost)
  const opinionWords = ['unpopular', 'controversial', 'truth', 'secret', 'mistake']
  const opinionCount = opinionWords.filter(word => caption.includes(word)).length
  viralPotential += opinionCount * 8

  // Brand alignment (based on tone consistency)
  let brandAlignment = 70 // base score

  // Length appropriateness
  const captionLength = caption.length
  if (content.platform === 'twitter' && captionLength <= 280) brandAlignment += 10
  if (content.platform === 'instagram' && captionLength >= 100 && captionLength <= 300) brandAlignment += 10
  if (content.platform === 'linkedin' && captionLength >= 150) brandAlignment += 10

  // Hashtag strategy
  const hashtagCount = content.hashtags.length
  const platformOptimal = platformConfig[content.platform as keyof typeof platformConfig]?.hashtagCount
  if (platformOptimal && hashtagCount >= platformOptimal.min && hashtagCount <= platformOptimal.max) {
    brandAlignment += 15
  }

  // Generate recommendations
  const recommendations: string[] = []

  if (engagementScore < 60) {
    recommendations.push('Add a question or call-to-action to boost engagement')
  }
  if (viralPotential < 40) {
    recommendations.push('Consider incorporating trending topics or current events')
  }
  if (brandAlignment < 70) {
    recommendations.push('Ensure content aligns with brand voice and platform best practices')
  }
  if (emojiCount === 0) {
    recommendations.push('Add 1-3 relevant emojis to increase visual appeal')
  }
  if (ctaCount === 0) {
    recommendations.push('Include a clear call-to-action to drive engagement')
  }

  return {
    engagementScore: Math.min(100, Math.max(0, engagementScore)),
    viralPotential: Math.min(100, Math.max(0, viralPotential)),
    brandAlignment: Math.min(100, Math.max(0, brandAlignment)),
    recommendations
  }
}

// Demo mode: Generate high-quality content when AI is unavailable
export async function generateDemoCalendarContent(
  brandData: BrandData,
  month: Date
): Promise<ContentItem[]> {
  console.log('🎭 Generating demo calendar content (AI service unavailable)')

  const monthStart = startOfMonth(month)
  const monthEnd = endOfMonth(month)
  const days = eachDayOfInterval({ start: monthStart, end: monthEnd })

  const content: ContentItem[] = []
  const themes = ['educational', 'promotional', 'inspirational', 'behind-the-scenes']

  // Generate content for each day
  for (const day of days) {
    // Skip weekends for LinkedIn
    const isWeekend = day.getDay() === 0 || day.getDay() === 6
    const platformsForDay = isWeekend
      ? brandData.platforms.filter(p => p !== 'LinkedIn')
      : brandData.platforms

    // Select 1-2 platforms per day
    const selectedPlatforms = platformsForDay
      .sort(() => Math.random() - 0.5)
      .slice(0, Math.random() > 0.7 ? 2 : 1)

    for (const platform of selectedPlatforms) {
      const theme = themes[Math.floor(Math.random() * themes.length)]

      content.push({
        id: uuidv4(),
        date: format(day, 'yyyy-MM-dd'),
        platform,
        type: getRandomContentType(platform),
        caption: generateEnhancedFallbackCaption(brandData, platform, theme),
        hashtags: generateEnhancedFallbackHashtags(brandData, platform),
        visualPrompt: generateEnhancedFallbackVisual(brandData, platform, getRandomContentType(platform)),
        status: 'draft' as const,
        engagement: { likes: 0, comments: 0, shares: 0 },
        bestTime: getRandomOptimalTime(platform)
      })
    }
  }

  return content.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
}

function getRandomContentType(platform: string): string {
  const normalizedPlatform = normalizePlatformName(platform)
  const config = platformConfig[normalizedPlatform as keyof typeof platformConfig]
  return config.types[Math.floor(Math.random() * config.types.length)]
}

function getRandomOptimalTime(platform: string): string {
  const normalizedPlatform = normalizePlatformName(platform)
  const config = platformConfig[normalizedPlatform as keyof typeof platformConfig]
  return config.optimalTimes[Math.floor(Math.random() * config.optimalTimes.length)]
}

// Helper function to get content suggestions for a specific theme
export function getContentSuggestions(theme: string, industry: string) {
  const industryKey = industry as keyof typeof industryTemplates
  const industryTemplate = industryTemplates[industryKey]

  if (industryTemplate && industryTemplate[theme as keyof typeof industryTemplate]) {
    return industryTemplate[theme as keyof typeof industryTemplate]
  }

  // Fallback to generic suggestions
  const suggestions = {
    educational: [
      `How-to guide for ${industry}`,
      `Common mistakes in ${industry}`,
      `Best practices for beginners`,
      `Industry trends explained`
    ],
    promotional: [
      'Product showcase',
      'Special offer announcement',
      'New feature highlight',
      'Customer success story'
    ],
    'behind-the-scenes': [
      'Team member spotlight',
      'Office tour',
      'Product development process',
      'Company culture moments'
    ]
  }

  return suggestions[theme as keyof typeof suggestions] || [
    'Engaging content idea',
    'Industry-relevant post',
    'Audience-focused content'
  ]
}