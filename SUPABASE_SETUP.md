# Supabase Setup Guide for ViralPath.ai

This guide will help you set up Supabase for your ViralPath.ai application, including authentication and brand management.

## Prerequisites

- A Supabase account (sign up at [supabase.com](https://supabase.com))
- Your ViralPath.ai project files

## Step 1: Create a New Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign in
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: `viralpath-ai` (or your preferred name)
   - **Database Password**: Create a strong password (save this!)
   - **Region**: Choose the region closest to your users
5. Click "Create new project"
6. Wait for the project to be created (this may take a few minutes)

## Step 2: Configure Database Tables

1. In your Supabase dashboard, go to the **SQL Editor**
2. Copy the contents of `supabase/migrations/001_create_brands_and_calendars.sql`
3. Paste it into the SQL Editor
4. Click "Run" to execute the migration

This will create:
- `brands` table for storing brand information
- `content_calendars` table for storing generated content
- Row Level Security (RLS) policies
- Necessary indexes and triggers

## Step 3: Configure Authentication

### Enable Email Authentication
1. Go to **Authentication** > **Settings**
2. Under **Auth Providers**, ensure **Email** is enabled
3. Configure email settings:
   - **Enable email confirmations**: Recommended for production
   - **Enable email change confirmations**: Recommended
   - **Enable secure email change**: Recommended

### Enable Google OAuth (Optional)
1. Go to **Authentication** > **Providers**
2. Find **Google** and click the toggle to enable it
3. You'll need to:
   - Create a Google Cloud Project
   - Enable Google+ API
   - Create OAuth 2.0 credentials
   - Add your Supabase callback URL: `https://your-project-ref.supabase.co/auth/v1/callback`
4. Enter your Google OAuth credentials in Supabase

## Step 4: Get Your Project Credentials

1. Go to **Settings** > **API**
2. Copy the following values:
   - **Project URL** (something like `https://your-project-ref.supabase.co`)
   - **Project API Key** (anon/public key)

## Step 5: Configure Environment Variables

1. In your project root, create a `.env.local` file:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here

# Optional: OpenAI API Key for AI content generation
OPENAI_API_KEY=your-openai-api-key-here
```

2. Replace the placeholder values with your actual Supabase credentials
3. **Important**: Never commit `.env.local` to version control

## Step 6: Test Your Setup

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Open [http://localhost:3000](http://localhost:3000)

3. Test the authentication flow:
   - Try signing up with a new email
   - Check if you receive a confirmation email (if enabled)
   - Try signing in
   - Test the brand management features

## Step 7: Configure Email Templates (Optional)

Customize your authentication emails:

1. Go to **Authentication** > **Email Templates**
2. Customize the templates for:
   - Confirm signup
   - Magic link
   - Change email address
   - Reset password

## Troubleshooting

### Common Issues

**"Invalid API key" error:**
- Double-check your environment variables
- Ensure you're using the anon/public key, not the service role key
- Restart your development server after changing environment variables

**Authentication not working:**
- Check if your Supabase project URL is correct
- Verify that email authentication is enabled
- Check the browser console for error messages

**Database errors:**
- Ensure the migration SQL was executed successfully
- Check the Supabase logs in the dashboard
- Verify that RLS policies are properly configured

**Google OAuth not working:**
- Verify your Google OAuth credentials
- Check that the callback URL is correctly configured
- Ensure the Google+ API is enabled in Google Cloud Console

### Getting Help

- Check the [Supabase Documentation](https://supabase.com/docs)
- Visit the [Supabase Community](https://github.com/supabase/supabase/discussions)
- Review the browser console and Supabase logs for error messages

## Security Best Practices

1. **Environment Variables**: Never expose your service role key in client-side code
2. **RLS Policies**: The migration includes Row Level Security policies to protect user data
3. **HTTPS**: Always use HTTPS in production
4. **Email Verification**: Enable email confirmation for production environments
5. **Rate Limiting**: Consider implementing rate limiting for authentication endpoints

## Next Steps

Once your Supabase setup is complete, you can:

1. Create your first brand in the Brand Manager
2. Generate content calendars for your brands
3. Explore the user profile management features
4. Set up additional authentication providers if needed
5. Configure custom email templates
6. Set up database backups and monitoring

---

**Note**: This setup guide assumes you're using the development environment. For production deployment, additional considerations like custom domains, SSL certificates, and performance optimization may be required.