# Database Migration Guide - Security Fixes

## 🚨 Important: Use These Corrected Migration Files

The original migration files (`004_fix_security_warnings.sql` and `005_fix_remaining_security_functions.sql`) had dependency issues. Use these corrected versions instead:

### ✅ Corrected Migration Files:
1. `supabase/migrations/006_fix_security_with_dependencies.sql`
2. `supabase/migrations/007_fix_remaining_security_functions_corrected.sql`

---

## 📋 Step-by-Step Migration Process

### Step 1: Apply First Migration
1. Go to your Supabase Dashboard
2. Navigate to **SQL Editor**
3. Copy and paste the contents of `006_fix_security_with_dependencies.sql`
4. Click **"Run"**
5. ✅ Should complete without errors

### Step 2: Apply Second Migration
1. In the same SQL Editor
2. Copy and paste the contents of `007_fix_remaining_security_functions_corrected.sql`
3. Click **"Run"**
4. ✅ Should complete without errors

### Step 3: Verify Success
You should see a success message:
```
Security fixes applied successfully - all 14 functions now use SECURITY DEFINER with fixed search_path
```

---

## 🔧 What These Migrations Fix

### Key Changes:
- **Uses `CREATE OR REPLACE`** instead of `DROP FUNCTION` to avoid dependency issues
- **Preserves all existing triggers** that depend on these functions
- **Adds `SECURITY DEFINER`** to all 14 functions
- **Sets fixed `search_path = public`** to prevent injection attacks

### Functions Fixed:
1. ✅ `update_updated_at_column()` - Used by 8 triggers
2. ✅ `handle_new_user()` - Auth trigger
3. ✅ `log_audit_event()` - Audit logging
4. ✅ `track_usage()` - Usage tracking
5. ✅ `check_usage_limit()` - Limit validation
6. ✅ `create_notification()` - Notifications
7. ✅ `get_user_feature_flags()` - Feature flags
8. ✅ `audit_brands_changes()` - Brand auditing
9. ✅ `handle_updated_at()` - Update handling
10. ✅ `ensure_single_active_brand()` - Brand logic
11. ✅ `create_content_version()` - Content versioning
12. ✅ `restore_content_version()` - Version restoration
13. ✅ `get_content_version_history()` - Version history
14. ✅ `cleanup_old_versions()` - Version cleanup

---

## 🐛 Troubleshooting

### If You Already Ran the Original Migrations:
If you encountered the dependency error, the functions are likely in a mixed state. Run the corrected migrations anyway - `CREATE OR REPLACE` will fix them.

### If You See Permission Errors:
Make sure you're using the **service role key** or have sufficient permissions in Supabase.

### If Functions Don't Exist:
Some functions might not exist in your database yet. The migrations use `CREATE OR REPLACE`, so they'll create missing functions.

---

## 🧪 Testing After Migration

### 1. Check Function Security
Run this query to verify all functions have proper security:
```sql
SELECT 
    proname as function_name,
    prosecdef as is_security_definer,
    proconfig as search_path_config
FROM pg_proc 
WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
AND proname IN (
    'update_updated_at_column',
    'handle_new_user',
    'log_audit_event',
    'track_usage',
    'check_usage_limit',
    'create_notification',
    'get_user_feature_flags',
    'audit_brands_changes',
    'handle_updated_at',
    'ensure_single_active_brand',
    'create_content_version',
    'restore_content_version',
    'get_content_version_history',
    'cleanup_old_versions'
);
```

Expected results:
- `is_security_definer` should be `true` for all functions
- `search_path_config` should show `{search_path=public}` for all functions

### 2. Test Application Functionality
1. Clear browser cache and refresh
2. Sign in to your application
3. Try creating/loading calendar content
4. Verify no console errors
5. Test brand switching
6. Test content generation

### 3. Run Supabase Database Linter
After applying migrations, run the Supabase database linter again to verify all security warnings are resolved.

---

## 🔍 Expected Results

### Before Migration:
- ❌ 14 security warnings about mutable search paths
- ❌ Functions vulnerable to search path injection
- ❌ Potential privilege escalation risks

### After Migration:
- ✅ All security warnings resolved
- ✅ All functions use `SECURITY DEFINER`
- ✅ Fixed search paths prevent injection attacks
- ✅ Existing triggers continue to work normally

---

## 📞 Support

### If You Encounter Issues:
1. **Check the Supabase logs** in your dashboard
2. **Verify your database schema** matches the expected structure
3. **Test with a simple function call** to ensure basic functionality works
4. **Check browser console** for any remaining errors

### Common Issues:
- **Empty error objects**: Fixed with improved error logging
- **Authentication errors**: Ensure user is properly signed in
- **RLS policy conflicts**: Already resolved in previous migrations

---

## 🎯 Next Steps After Migration

1. ✅ **Apply both migration files**
2. ✅ **Test application functionality**
3. ✅ **Configure auth security settings** (see `configure-auth-security.md`)
4. ✅ **Run security verification tests**
5. ✅ **Monitor for any issues**

---

*Migration guide created: January 8, 2025*  
*Use corrected migration files to avoid dependency issues*
