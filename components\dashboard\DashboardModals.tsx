'use client'

import ContentModal from '@/components/ContentModal'
import ExportModal from '@/components/ExportModal'
import UserProfile from '@/components/UserProfile'
import BrandManager from '@/components/BrandManager'
import VersionHistory from '@/components/VersionHistory'
import GenerationProgress from '@/components/GenerationProgress'
import { ContentItem } from '@/lib/types/content'
import { Database } from '@/lib/supabase/types'

type DatabaseBrand = Database['public']['Tables']['brands']['Row']

interface Brand {
  id: string
  name: string
  industry: string
}

interface BrandData {
  name: string
  industry: string
  tone: string
  targetAudience: string
  platforms: string[]
  description: string
  website: string
}

// ContentItem is imported from '@/lib/types/content'

interface DashboardModalsProps {
  // Modal states
  showContentModal: boolean
  showExportModal: boolean
  showUserProfile: boolean
  showBrandManager: boolean
  showVersionHistory: boolean
  isGenerating: boolean

  // Data
  selectedContent: ContentItem | null
  brandData: BrandData | null
  currentBrand: DatabaseBrand | null
  user: { id: string; email?: string } | null
  currentMonth: Date | null
  calendarContent: ContentItem[]

  // Handlers
  onContentModalClose: () => void
  onExportModalClose: () => void
  onUserProfileClose: () => void
  onBrandManagerClose: () => void
  onVersionHistoryClose: () => void
  onContentUpdate: (content: ContentItem, isAutoSave?: boolean) => void
  onVersionRestore: (content: ContentItem[]) => void
  onBrandChange?: (brand: any) => void
}

export default function DashboardModals({
  showContentModal,
  showExportModal,
  showUserProfile,
  showBrandManager,
  showVersionHistory,
  isGenerating,
  selectedContent,
  brandData,
  currentBrand,
  user,
  currentMonth,
  calendarContent,
  onContentModalClose,
  onExportModalClose,
  onUserProfileClose,
  onBrandManagerClose,
  onVersionHistoryClose,
  onContentUpdate,
  onVersionRestore,
  onBrandChange
}: DashboardModalsProps) {
  return (
    <>
      {/* Content Modal */}
      {showContentModal && selectedContent && (
        <ContentModal
          content={selectedContent}
          onClose={onContentModalClose}
          onUpdate={onContentUpdate}
          brandData={brandData ? {
            brandName: brandData.name,
            industry: brandData.industry,
            tone: brandData.tone,
            targetAudience: brandData.targetAudience,
            platforms: brandData.platforms
          } : undefined}
        />
      )}

      {/* Export Modal */}
      {showExportModal && brandData && (
        <ExportModal
          content={calendarContent}
          brandData={{
            brandName: brandData.name,
            industry: brandData.industry,
            tone: brandData.tone,
            targetAudience: brandData.targetAudience,
            platforms: brandData.platforms
          }}
          onClose={onExportModalClose}
        />
      )}

      {/* User Profile Modal */}
      {showUserProfile && user && (
        <UserProfile
          user={user}
          onClose={onUserProfileClose}
        />
      )}

      {/* Brand Manager Modal */}
      {showBrandManager && user && (
        <BrandManager
          userId={user.id}
          currentBrand={currentBrand}
          onBrandChange={onBrandChange || (() => { })}
          onClose={onBrandManagerClose}
        />
      )}

      {/* Version History Modal */}
      {showVersionHistory && currentBrand && user && currentMonth && (
        <VersionHistory
          brandId={currentBrand.id}
          userId={user.id}
          month={currentMonth.getMonth() + 1}
          year={currentMonth.getFullYear()}
          onClose={onVersionHistoryClose}
          onVersionRestore={onVersionRestore}
        />
      )}

      {/* Generation Progress */}
      <GenerationProgress isGenerating={isGenerating} />
    </>
  )
}
