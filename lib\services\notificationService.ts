import { createClient } from '@/lib/supabase/client'
import { Database, NotificationType } from '@/lib/supabase/database.types'

type Notification = Database['public']['Tables']['notifications']['Row']
type NotificationInsert = Database['public']['Tables']['notifications']['Insert']

export class NotificationService {
  private static supabase = createClient()

  /**
   * Create a new notification
   */
  static async createNotification(
    userId: string,
    type: NotificationType,
    title: string,
    message: string,
    actionUrl?: string,
    metadata: Record<string, any> = {}
  ): Promise<string | null> {
    try {
      const { data, error } = await this.supabase.rpc('create_notification', {
        p_user_id: userId,
        p_type: type,
        p_title: title,
        p_message: message,
        p_action_url: actionUrl,
        p_metadata: metadata
      })

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating notification:', error)
      return null
    }
  }

  /**
   * Get user notifications
   */
  static async getUserNotifications(
    userId: string,
    limit: number = 50,
    unreadOnly: boolean = false
  ): Promise<Notification[]> {
    try {
      let query = this.supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (unreadOnly) {
        query = query.eq('is_read', false)
      }

      const { data, error } = await query

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching notifications:', error)
      return []
    }
  }

  /**
   * Mark notification as read
   */
  static async markAsRead(notificationId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId)

      if (error) throw error
      return true
    } catch (error) {
      console.error('Error marking notification as read:', error)
      return false
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  static async markAllAsRead(userId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('user_id', userId)
        .eq('is_read', false)

      if (error) throw error
      return true
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      return false
    }
  }

  /**
   * Delete notification
   */
  static async deleteNotification(notificationId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId)

      if (error) throw error
      return true
    } catch (error) {
      console.error('Error deleting notification:', error)
      return false
    }
  }

  /**
   * Get unread notification count
   */
  static async getUnreadCount(userId: string): Promise<number> {
    try {
      const { count, error } = await this.supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('is_read', false)

      if (error) throw error
      return count || 0
    } catch (error) {
      console.error('Error fetching unread count:', error)
      return 0
    }
  }

  /**
   * Subscribe to real-time notifications
   */
  static subscribeToNotifications(
    userId: string,
    callback: (notification: Notification) => void
  ) {
    return this.supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          callback(payload.new as Notification)
        }
      )
      .subscribe()
  }

  /**
   * Predefined notification templates
   */
  static async sendWelcomeNotification(userId: string): Promise<string | null> {
    return this.createNotification(
      userId,
      'system',
      'Welcome to ViralPath.ai! 🎉',
      'Get started by creating your first brand and generating viral content.',
      '/dashboard/onboarding'
    )
  }

  static async sendTrialExpiringNotification(
    userId: string,
    daysRemaining: number
  ): Promise<string | null> {
    return this.createNotification(
      userId,
      'billing',
      `Trial expires in ${daysRemaining} days`,
      `Your free trial will expire soon. Upgrade to continue using ViralPath.ai.`,
      '/dashboard/billing'
    )
  }

  static async sendUsageLimitNotification(
    userId: string,
    resourceType: string,
    percentage: number
  ): Promise<string | null> {
    const title = percentage >= 100 
      ? `${resourceType} limit reached`
      : `${resourceType} limit warning`
    
    const message = percentage >= 100
      ? `You've reached your ${resourceType} limit. Upgrade your plan to continue.`
      : `You've used ${percentage}% of your ${resourceType} limit.`

    return this.createNotification(
      userId,
      'system',
      title,
      message,
      '/dashboard/billing'
    )
  }

  static async sendContentGeneratedNotification(
    userId: string,
    brandName: string,
    contentCount: number
  ): Promise<string | null> {
    return this.createNotification(
      userId,
      'content',
      'Content generated successfully! ✨',
      `Generated ${contentCount} pieces of content for ${brandName}.`,
      '/dashboard'
    )
  }

  static async sendPaymentSuccessNotification(
    userId: string,
    amount: string,
    plan: string
  ): Promise<string | null> {
    return this.createNotification(
      userId,
      'billing',
      'Payment successful! 💳',
      `Your payment of ${amount} for the ${plan} plan has been processed.`,
      '/dashboard/billing'
    )
  }

  static async sendPaymentFailedNotification(
    userId: string,
    amount: string
  ): Promise<string | null> {
    return this.createNotification(
      userId,
      'billing',
      'Payment failed ❌',
      `Your payment of ${amount} could not be processed. Please update your payment method.`,
      '/dashboard/billing'
    )
  }

  static async sendFeatureAnnouncementNotification(
    userId: string,
    featureName: string,
    description: string
  ): Promise<string | null> {
    return this.createNotification(
      userId,
      'feature',
      `New feature: ${featureName} 🚀`,
      description,
      '/dashboard'
    )
  }

  /**
   * Get notification icon based on type
   */
  static getNotificationIcon(type: NotificationType): string {
    const icons = {
      system: '🔔',
      billing: '💳',
      content: '📝',
      feature: '🚀'
    }
    return icons[type] || '📢'
  }

  /**
   * Get notification color based on type
   */
  static getNotificationColor(type: NotificationType): string {
    const colors = {
      system: 'blue',
      billing: 'green',
      content: 'purple',
      feature: 'orange'
    }
    return colors[type] || 'gray'
  }

  /**
   * Format notification time
   */
  static formatNotificationTime(createdAt: string): string {
    const now = new Date()
    const notificationTime = new Date(createdAt)
    const diffInMinutes = Math.floor((now.getTime() - notificationTime.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    
    return notificationTime.toLocaleDateString()
  }

  /**
   * Clean up old notifications (older than 30 days)
   */
  static async cleanupOldNotifications(userId: string): Promise<boolean> {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      
      const { error } = await this.supabase
        .from('notifications')
        .delete()
        .eq('user_id', userId)
        .eq('is_read', true)
        .lt('created_at', thirtyDaysAgo.toISOString())

      if (error) throw error
      return true
    } catch (error) {
      console.error('Error cleaning up old notifications:', error)
      return false
    }
  }
}
