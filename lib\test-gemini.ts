// Test script to verify Gemini integration
// This can be used to test the Gemini service independently

import { GeminiService } from './services/geminiService'

export async function testGeminiIntegration() {
  console.log('🧪 Testing Google Gemini integration...')
  
  try {
    // Test API key validation
    console.log('🔑 Validating Gemini API key...')
    const isValidKey = await GeminiService.validateApiKey()
    
    if (!isValidKey) {
      console.error('❌ Gemini API key validation failed')
      return false
    }
    
    console.log('✅ Gemini API key is valid')
    
    // Test content generation
    console.log('🎯 Testing content generation with Gemini 1.5 Flash...')
    
    const testPrompt = `Create a social media post for a tech startup about AI innovation. 
    Respond with JSON containing:
    {
      "caption": "engaging social media caption",
      "hashtags": ["#hashtag1", "#hashtag2"],
      "visualPrompt": "description for visual content"
    }`
    
    const response = await GeminiService.generateContent(
      'gemini-1.5-flash',
      testPrompt,
      {
        temperature: 0.7,
        max_tokens: 500,
        top_p: 0.95
      }
    )
    
    console.log('✅ Gemini content generation successful!')
    console.log('Generated response:', response.substring(0, 200) + '...')
    
    // Try to parse the JSON response
    try {
      const parsed = JSON.parse(response)
      if (parsed.caption && parsed.hashtags && parsed.visualPrompt) {
        console.log('✅ Response format is valid!')
        console.log('Caption:', parsed.caption.substring(0, 50) + '...')
        console.log('Hashtags:', parsed.hashtags)
        return true
      } else {
        console.warn('⚠️ Response missing required fields')
        return false
      }
    } catch (parseError) {
      console.warn('⚠️ Response is not valid JSON, but generation worked')
      return true // Still consider it a success if the API call worked
    }
    
  } catch (error) {
    console.error('❌ Gemini test failed:', error)
    return false
  }
}

export async function testGeminiModels() {
  console.log('🧪 Testing all Gemini models...')
  
  const models = ['gemini-1.5-flash', 'gemini-1.5-pro']
  const results: Record<string, boolean> = {}
  
  for (const model of models) {
    try {
      console.log(`🎯 Testing ${model}...`)
      
      const response = await GeminiService.generateContent(
        model,
        'Generate a simple JSON response with {"test": "success"}',
        GeminiService.getGeminiSettings(model)
      )
      
      results[model] = true
      console.log(`✅ ${model} working`)
      
    } catch (error) {
      console.error(`❌ ${model} failed:`, error)
      results[model] = false
    }
  }
  
  return results
}

// Uncomment to run tests
// testGeminiIntegration()
// testGeminiModels()
