'use client'

import { cn } from '@/lib/utils/cn'

interface SkeletonProps {
  className?: string
  children?: React.ReactNode
}

export function Skeleton({ className, children, ...props }: SkeletonProps & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "animate-pulse rounded-md bg-gray-200",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

// Calendar Grid Skeleton
export function CalendarGridSkeleton() {
  return (
    <div className="grid grid-cols-7 gap-2">
      {/* Days of week header */}
      {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
        <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
          {day}
        </div>
      ))}
      
      {/* Calendar days */}
      {Array.from({ length: 35 }).map((_, index) => (
        <div key={index} className="aspect-square border border-gray-200 rounded-lg p-2">
          <Skeleton className="h-4 w-6 mb-2" />
          <div className="space-y-1">
            <Skeleton className="h-2 w-full" />
            <Skeleton className="h-2 w-3/4" />
          </div>
        </div>
      ))}
    </div>
  )
}

// Brand Card Skeleton
export function BrandCardSkeleton() {
  return (
    <div className="card">
      <div className="flex items-center gap-3 mb-4">
        <Skeleton className="w-12 h-12 rounded-lg" />
        <div className="flex-1">
          <Skeleton className="h-5 w-32 mb-2" />
          <Skeleton className="h-4 w-24" />
        </div>
      </div>
      <div className="space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
      </div>
      <div className="flex gap-2 mt-4">
        <Skeleton className="h-6 w-16 rounded-full" />
        <Skeleton className="h-6 w-20 rounded-full" />
        <Skeleton className="h-6 w-18 rounded-full" />
      </div>
    </div>
  )
}

// Content Modal Skeleton
export function ContentModalSkeleton() {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <Skeleton className="w-10 h-10 rounded-lg" />
            <div>
              <Skeleton className="h-6 w-32 mb-2" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-10 rounded-lg" />
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column */}
            <div className="space-y-6">
              <div>
                <Skeleton className="h-5 w-24 mb-3" />
                <Skeleton className="h-32 w-full rounded-lg" />
              </div>
              <div>
                <Skeleton className="h-5 w-20 mb-3" />
                <div className="flex flex-wrap gap-2">
                  <Skeleton className="h-6 w-16 rounded-md" />
                  <Skeleton className="h-6 w-20 rounded-md" />
                  <Skeleton className="h-6 w-18 rounded-md" />
                </div>
              </div>
              <div>
                <Skeleton className="h-5 w-32 mb-3" />
                <Skeleton className="h-12 w-full rounded-lg" />
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              <div>
                <Skeleton className="h-5 w-36 mb-3" />
                <Skeleton className="h-40 w-full rounded-lg" />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Skeleton className="h-5 w-24 mb-2" />
                  <Skeleton className="h-12 w-full rounded-lg" />
                </div>
                <div>
                  <Skeleton className="h-5 w-20 mb-2" />
                  <Skeleton className="h-12 w-full rounded-lg" />
                </div>
              </div>
              <Skeleton className="h-24 w-full rounded-lg" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Stats Card Skeleton
export function StatsCardSkeleton() {
  return (
    <div className="card">
      <div className="flex items-center gap-2 mb-4">
        <Skeleton className="w-5 h-5" />
        <Skeleton className="h-5 w-20" />
      </div>
      <div className="space-y-4">
        <div>
          <Skeleton className="h-8 w-12 mb-1" />
          <Skeleton className="h-4 w-16" />
        </div>
        <div>
          <Skeleton className="h-8 w-8 mb-1" />
          <Skeleton className="h-4 w-20" />
        </div>
      </div>
    </div>
  )
}

// Platform Breakdown Skeleton
export function PlatformBreakdownSkeleton() {
  return (
    <div className="card">
      <Skeleton className="h-5 w-32 mb-4" />
      <div className="space-y-3">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="flex items-center justify-between">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-6" />
          </div>
        ))}
      </div>
    </div>
  )
}

// Brand Settings Skeleton
export function BrandSettingsSkeleton() {
  return (
    <div className="card">
      <div className="flex items-center gap-2 mb-4">
        <Skeleton className="w-5 h-5" />
        <Skeleton className="h-5 w-28" />
      </div>
      <div className="space-y-3">
        <div>
          <Skeleton className="h-4 w-full" />
        </div>
        <div>
          <Skeleton className="h-4 w-3/4" />
        </div>
      </div>
      <Skeleton className="h-8 w-full mt-4" />
    </div>
  )
}

// Loading Spinner Component
export function LoadingSpinner({ size = 'md', className }: { size?: 'sm' | 'md' | 'lg', className?: string }) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  }

  return (
    <div className={cn(
      "animate-spin rounded-full border-b-2 border-primary-purple",
      sizeClasses[size],
      className
    )} />
  )
}

// Progress Bar Component
export function ProgressBar({ progress, className }: { progress: number, className?: string }) {
  return (
    <div className={cn("w-full bg-gray-200 rounded-full h-2", className)}>
      <div 
        className="bg-gradient-primary h-2 rounded-full transition-all duration-300 ease-out"
        style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
      />
    </div>
  )
}
