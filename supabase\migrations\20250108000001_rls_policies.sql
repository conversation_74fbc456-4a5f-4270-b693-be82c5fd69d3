-- Row Level Security (RLS) Policies for ViralPath.ai
-- Ensures users can only access their own data

-- Enable RLS on all tables
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.brands ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_calendars ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_model_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.webhooks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.feature_flags ENABLE ROW LEVEL SECURITY;

-- User Profiles Policies
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Subscriptions Policies
CREATE POLICY "Users can view own subscriptions" ON public.subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own subscriptions" ON public.subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own subscriptions" ON public.subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Usage Tracking Policies
CREATE POLICY "Users can view own usage" ON public.usage_tracking
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own usage" ON public.usage_tracking
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Brands Policies
CREATE POLICY "Users can view own brands" ON public.brands
    FOR SELECT USING (
        auth.uid() = user_id OR 
        auth.uid() IN (
            SELECT member_id FROM public.team_members 
            WHERE team_owner_id = user_id AND is_active = true
        )
    );

CREATE POLICY "Users can insert own brands" ON public.brands
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own brands" ON public.brands
    FOR UPDATE USING (
        auth.uid() = user_id OR 
        auth.uid() IN (
            SELECT member_id FROM public.team_members 
            WHERE team_owner_id = user_id AND is_active = true 
            AND 'brands:write' = ANY(permissions)
        )
    );

CREATE POLICY "Users can delete own brands" ON public.brands
    FOR DELETE USING (auth.uid() = user_id);

-- Content Calendars Policies
CREATE POLICY "Users can view own content calendars" ON public.content_calendars
    FOR SELECT USING (
        auth.uid() = user_id OR 
        auth.uid() IN (
            SELECT member_id FROM public.team_members 
            WHERE team_owner_id = user_id AND is_active = true
        )
    );

CREATE POLICY "Users can insert own content calendars" ON public.content_calendars
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own content calendars" ON public.content_calendars
    FOR UPDATE USING (
        auth.uid() = user_id OR 
        auth.uid() IN (
            SELECT member_id FROM public.team_members 
            WHERE team_owner_id = user_id AND is_active = true 
            AND 'content:write' = ANY(permissions)
        )
    );

CREATE POLICY "Users can delete own content calendars" ON public.content_calendars
    FOR DELETE USING (auth.uid() = user_id);

-- Content Posts Policies
CREATE POLICY "Users can view own content posts" ON public.content_posts
    FOR SELECT USING (
        auth.uid() = user_id OR 
        auth.uid() IN (
            SELECT member_id FROM public.team_members 
            WHERE team_owner_id = user_id AND is_active = true
        )
    );

CREATE POLICY "Users can insert own content posts" ON public.content_posts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own content posts" ON public.content_posts
    FOR UPDATE USING (
        auth.uid() = user_id OR 
        auth.uid() IN (
            SELECT member_id FROM public.team_members 
            WHERE team_owner_id = user_id AND is_active = true 
            AND 'content:write' = ANY(permissions)
        )
    );

CREATE POLICY "Users can delete own content posts" ON public.content_posts
    FOR DELETE USING (auth.uid() = user_id);

-- Content Templates Policies
CREATE POLICY "Users can view own and public templates" ON public.content_templates
    FOR SELECT USING (
        auth.uid() = user_id OR 
        is_public = true OR
        auth.uid() IN (
            SELECT member_id FROM public.team_members 
            WHERE team_owner_id = user_id AND is_active = true
        )
    );

CREATE POLICY "Users can insert own templates" ON public.content_templates
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own templates" ON public.content_templates
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own templates" ON public.content_templates
    FOR DELETE USING (auth.uid() = user_id);

-- AI Model Usage Policies
CREATE POLICY "Users can view own AI usage" ON public.ai_model_usage
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own AI usage" ON public.ai_model_usage
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Notifications Policies
CREATE POLICY "Users can view own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- Audit Logs Policies (read-only for users)
CREATE POLICY "Users can view own audit logs" ON public.audit_logs
    FOR SELECT USING (auth.uid() = user_id);

-- API Keys Policies
CREATE POLICY "Users can view own API keys" ON public.api_keys
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own API keys" ON public.api_keys
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own API keys" ON public.api_keys
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own API keys" ON public.api_keys
    FOR DELETE USING (auth.uid() = user_id);

-- Webhooks Policies
CREATE POLICY "Users can view own webhooks" ON public.webhooks
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own webhooks" ON public.webhooks
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own webhooks" ON public.webhooks
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own webhooks" ON public.webhooks
    FOR DELETE USING (auth.uid() = user_id);

-- Team Members Policies
CREATE POLICY "Team owners can view their team members" ON public.team_members
    FOR SELECT USING (auth.uid() = team_owner_id OR auth.uid() = member_id);

CREATE POLICY "Team owners can insert team members" ON public.team_members
    FOR INSERT WITH CHECK (auth.uid() = team_owner_id);

CREATE POLICY "Team owners can update team members" ON public.team_members
    FOR UPDATE USING (auth.uid() = team_owner_id);

CREATE POLICY "Team owners can delete team members" ON public.team_members
    FOR DELETE USING (auth.uid() = team_owner_id);

-- Feature Flags Policies (read-only for users)
CREATE POLICY "Users can view enabled feature flags" ON public.feature_flags
    FOR SELECT USING (
        is_enabled = true AND (
            rollout_percentage = 100 OR
            auth.uid() = ANY(target_users) OR
            EXISTS (
                SELECT 1 FROM public.subscriptions s 
                WHERE s.user_id = auth.uid() AND s.tier = ANY(target_tiers)
            )
        )
    );
