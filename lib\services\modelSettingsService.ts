// Model Settings Service
// Manages AI model selection and configuration

export interface ModelSettings {
  selectedModel: string
  apiKey?: string
  customSettings?: Record<string, any>
}

const DEFAULT_MODEL = 'openai/gpt-oss-20b:free'
const STORAGE_KEY = 'viralpath_model_settings'

export class ModelSettingsService {
  /**
   * Get current model settings
   */
  static getModelSettings(): ModelSettings {
    if (typeof window === 'undefined') {
      return { selectedModel: DEFAULT_MODEL }
    }

    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const settings = JSON.parse(stored)
        return {
          selectedModel: settings.selectedModel || DEFAULT_MODEL,
          apiKey: settings.apiKey,
          customSettings: settings.customSettings || {}
        }
      }
    } catch (error) {
      console.error('Error loading model settings:', error)
    }

    return { selectedModel: DEFAULT_MODEL }
  }

  /**
   * Save model settings
   */
  static saveModelSettings(settings: ModelSettings): void {
    if (typeof window === 'undefined') return

    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(settings))
    } catch (error) {
      console.error('Error saving model settings:', error)
    }
  }

  /**
   * Update selected model
   */
  static updateSelectedModel(modelId: string): void {
    const currentSettings = this.getModelSettings()
    this.saveModelSettings({
      ...currentSettings,
      selectedModel: modelId
    })
  }

  /**
   * Get current selected model
   */
  static getCurrentModel(): string {
    return this.getModelSettings().selectedModel
  }

  /**
   * Check if model is a Gemini model
   */
  static isGeminiModel(modelId: string): boolean {
    return modelId.startsWith('gemini-')
  }

  /**
   * Check if model is free tier
   */
  static isFreeTierModel(modelId: string): boolean {
    const freeTierModels = [
      'openai/gpt-oss-20b:free',
      'deepseek/deepseek-chat-v3-0324:free',
      'microsoft/wizardlm-2-8x22b:free',
      'google/gemma-2-9b-it:free'
    ]
    return freeTierModels.includes(modelId)
  }

  /**
   * Get model display information
   */
  static getModelInfo(modelId: string) {
    const modelMap: Record<string, { name: string; provider: string; tier: string }> = {
      'openai/gpt-oss-20b:free': { name: 'GPT OSS 20B', provider: 'OpenAI', tier: 'Free' },
      'openai/gpt-3.5-turbo': { name: 'GPT-3.5 Turbo', provider: 'OpenAI', tier: 'Paid' },
      'openai/gpt-4': { name: 'GPT-4', provider: 'OpenAI', tier: 'Paid' },
      'anthropic/claude-3-haiku': { name: 'Claude 3 Haiku', provider: 'Anthropic', tier: 'Paid' },
      'anthropic/claude-3-sonnet': { name: 'Claude 3 Sonnet', provider: 'Anthropic', tier: 'Paid' },
      'deepseek/deepseek-chat-v3-0324:free': { name: 'DeepSeek V3 0324', provider: 'DeepSeek', tier: 'Free' },
      'microsoft/wizardlm-2-8x22b:free': { name: 'WizardLM 2 8x22B', provider: 'Microsoft', tier: 'Free' },
      'google/gemma-2-9b-it:free': { name: 'Gemma 2 9B', provider: 'Google', tier: 'Free' },
      // Legacy Gemini models
      'gemini-1.5-flash': { name: 'Gemini 1.5 Flash (Legacy)', provider: 'Google', tier: 'Paid' },
      'gemini-1.5-pro': { name: 'Gemini 1.5 Pro (Legacy)', provider: 'Google', tier: 'Paid' },
      // Latest Gemini 2.5 models
      'gemini-2.5-pro': { name: 'Gemini 2.5 Pro', provider: 'Google', tier: 'Paid' },
      'gemini-2.5-flash': { name: 'Gemini 2.5 Flash', provider: 'Google', tier: 'Paid' },
      'gemini-2.5-flash-lite': { name: 'Gemini 2.5 Flash Lite', provider: 'Google', tier: 'Paid' },
      // Latest Gemini 2.0 models
      'gemini-2.0-flash-exp': { name: 'Gemini 2.0 Flash (Experimental)', provider: 'Google', tier: 'Free' },
      'gemini-2.0-flash': { name: 'Gemini 2.0 Flash', provider: 'Google', tier: 'Paid' },
      'gemini-2.0-flash-lite': { name: 'Gemini 2.0 Flash Lite', provider: 'Google', tier: 'Paid' }
    }

    return modelMap[modelId] || { name: 'Unknown Model', provider: 'Unknown', tier: 'Unknown' }
  }

  /**
   * Validate model availability
   */
  static async validateModel(modelId: string): Promise<boolean> {
    // List of known working models (updated regularly)
    const workingModels = [
      'openai/gpt-oss-20b:free',
      'deepseek/deepseek-chat-v3-0324:free',
      'microsoft/wizardlm-2-8x22b:free',
      'google/gemma-2-9b-it:free',
      'openai/gpt-3.5-turbo',
      'openai/gpt-4',
      'anthropic/claude-3-haiku',
      'anthropic/claude-3-sonnet',
      // Legacy Gemini models
      'gemini-1.5-flash',
      'gemini-1.5-pro',
      // Latest Gemini 2.5 models
      'gemini-2.5-pro',
      'gemini-2.5-flash',
      'gemini-2.5-flash-lite',
      // Latest Gemini 2.0 models
      'gemini-2.0-flash',
      'gemini-2.0-flash-lite'
    ]

    return workingModels.includes(modelId)
  }

  /**
   * Get recommended settings for a model
   */
  static getRecommendedSettings(modelId: string) {
    const settingsMap: Record<string, any> = {
      'openai/gpt-oss-20b:free': {
        temperature: 0.8,
        max_tokens: 1000,
        top_p: 1
      },
      'openai/gpt-3.5-turbo': {
        temperature: 0.7,
        max_tokens: 1500,
        top_p: 1
      },
      'openai/gpt-4': {
        temperature: 0.7,
        max_tokens: 2000,
        top_p: 1
      },
      'anthropic/claude-3-haiku': {
        temperature: 0.7,
        max_tokens: 1500,
        top_p: 1
      },
      'anthropic/claude-3-sonnet': {
        temperature: 0.7,
        max_tokens: 2000,
        top_p: 1
      },
      'deepseek/deepseek-chat-v3-0324:free': {
        temperature: 0.7,
        max_tokens: 1200,
        top_p: 0.9
      },
      'microsoft/wizardlm-2-8x22b:free': {
        temperature: 0.8,
        max_tokens: 1000,
        top_p: 1
      },
      'google/gemma-2-9b-it:free': {
        temperature: 0.7,
        max_tokens: 1000,
        top_p: 0.95
      },
      // Legacy Gemini models
      'gemini-1.5-flash': {
        temperature: 0.7,
        max_tokens: 1500,
        top_p: 0.95
      },
      'gemini-1.5-pro': {
        temperature: 0.7,
        max_tokens: 2000,
        top_p: 0.9
      },
      // Latest Gemini 2.5 models - optimized for viral content
      'gemini-2.5-pro': {
        temperature: 0.85,
        max_tokens: 2500,
        top_p: 0.92
      },
      'gemini-2.5-flash': {
        temperature: 0.8,
        max_tokens: 2000,
        top_p: 0.9
      },
      'gemini-2.5-flash-lite': {
        temperature: 0.8,
        max_tokens: 1500,
        top_p: 0.9
      },
      // Latest Gemini 2.0 models - optimized for viral content
      'gemini-2.0-flash': {
        temperature: 0.85,
        max_tokens: 2000,
        top_p: 0.92
      },
      'gemini-2.0-flash-lite': {
        temperature: 0.8,
        max_tokens: 1500,
        top_p: 0.9
      }
    }

    return settingsMap[modelId] || {
      temperature: 0.7,
      max_tokens: 1000,
      top_p: 1
    }
  }

  /**
   * Reset to default settings
   */
  static resetToDefaults(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(STORAGE_KEY)
    }
  }

  /**
   * Export settings for backup
   */
  static exportSettings(): string {
    const settings = this.getModelSettings()
    return JSON.stringify(settings, null, 2)
  }

  /**
   * Import settings from backup
   */
  static importSettings(settingsJson: string): boolean {
    try {
      const settings = JSON.parse(settingsJson)
      if (settings.selectedModel) {
        this.saveModelSettings(settings)
        return true
      }
    } catch (error) {
      console.error('Error importing settings:', error)
    }
    return false
  }
}
