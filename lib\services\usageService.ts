import { createClient } from '@/lib/supabase/client'
import { Database } from '@/lib/supabase/database.types'

type UsageTracking = Database['public']['Tables']['usage_tracking']['Row']
type UsageTrackingInsert = Database['public']['Tables']['usage_tracking']['Insert']

interface UsageLimit {
  current_usage: number
  usage_limit: number
  remaining: number
  percentage_used: number
  is_over_limit: boolean
}

export class UsageService {
  private static supabase = createClient()

  /**
   * Track usage for a user
   */
  static async trackUsage(
    userId: string,
    resourceType: string,
    amount: number = 1,
    metadata: Record<string, any> = {}
  ): Promise<string | null> {
    try {
      const { data, error } = await this.supabase.rpc('track_usage', {
        p_user_id: userId,
        p_resource_type: resourceType,
        p_amount: amount,
        p_metadata: metadata
      })

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error tracking usage:', error)
      return null
    }
  }

  /**
   * Check usage limits for a user
   */
  static async checkUsageLimit(
    userId: string,
    resourceType: string,
    periodDays: number = 30
  ): Promise<UsageLimit | null> {
    try {
      const { data, error } = await this.supabase.rpc('check_usage_limit', {
        p_user_id: userId,
        p_resource_type: resourceType,
        p_period_days: periodDays
      })

      if (error) throw error
      return data as UsageLimit
    } catch (error) {
      console.error('Error checking usage limit:', error)
      return null
    }
  }

  /**
   * Get usage history for a user
   */
  static async getUsageHistory(
    userId: string,
    resourceType?: string,
    days: number = 30
  ): Promise<UsageTracking[]> {
    try {
      let query = this.supabase
        .from('usage_tracking')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false })

      if (resourceType) {
        query = query.eq('resource_type', resourceType)
      }

      const { data, error } = await query

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching usage history:', error)
      return []
    }
  }

  /**
   * Get usage summary by resource type
   */
  static async getUsageSummary(
    userId: string,
    days: number = 30
  ): Promise<Record<string, number>> {
    try {
      const { data, error } = await this.supabase
        .from('usage_tracking')
        .select('resource_type, amount')
        .eq('user_id', userId)
        .gte('created_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())

      if (error) throw error

      const summary: Record<string, number> = {}
      data?.forEach(item => {
        summary[item.resource_type] = (summary[item.resource_type] || 0) + item.amount
      })

      return summary
    } catch (error) {
      console.error('Error fetching usage summary:', error)
      return {}
    }
  }

  /**
   * Get daily usage for charts
   */
  static async getDailyUsage(
    userId: string,
    resourceType: string,
    days: number = 30
  ): Promise<Array<{ date: string; usage: number }>> {
    try {
      const { data, error } = await this.supabase
        .from('usage_tracking')
        .select('created_at, amount')
        .eq('user_id', userId)
        .eq('resource_type', resourceType)
        .gte('created_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: true })

      if (error) throw error

      // Group by date
      const dailyUsage: Record<string, number> = {}
      data?.forEach(item => {
        const date = new Date(item.created_at).toISOString().split('T')[0]
        dailyUsage[date] = (dailyUsage[date] || 0) + item.amount
      })

      // Convert to array format
      return Object.entries(dailyUsage).map(([date, usage]) => ({
        date,
        usage
      }))
    } catch (error) {
      console.error('Error fetching daily usage:', error)
      return []
    }
  }

  /**
   * Check if user can perform action based on usage limits
   */
  static async canPerformAction(
    userId: string,
    resourceType: string,
    amount: number = 1
  ): Promise<{ allowed: boolean; reason?: string; limit?: UsageLimit }> {
    try {
      const limit = await this.checkUsageLimit(userId, resourceType)
      
      if (!limit) {
        return { allowed: false, reason: 'Unable to check usage limits' }
      }

      if (limit.usage_limit === -1) {
        // Unlimited
        return { allowed: true, limit }
      }

      if (limit.current_usage + amount > limit.usage_limit) {
        return {
          allowed: false,
          reason: `Usage limit exceeded. You have used ${limit.current_usage}/${limit.usage_limit} for this period.`,
          limit
        }
      }

      return { allowed: true, limit }
    } catch (error) {
      console.error('Error checking if action is allowed:', error)
      return { allowed: false, reason: 'Error checking usage limits' }
    }
  }

  /**
   * Get usage percentage for display
   */
  static getUsagePercentage(current: number, limit: number): number {
    if (limit === -1) return 0 // Unlimited
    if (limit === 0) return 100
    return Math.min(100, Math.round((current / limit) * 100))
  }

  /**
   * Get usage status color for UI
   */
  static getUsageStatusColor(percentage: number): string {
    if (percentage >= 100) return 'red'
    if (percentage >= 80) return 'orange'
    if (percentage >= 60) return 'yellow'
    return 'green'
  }

  /**
   * Format usage display text
   */
  static formatUsageDisplay(current: number, limit: number): string {
    if (limit === -1) return `${current.toLocaleString()} (Unlimited)`
    return `${current.toLocaleString()} / ${limit.toLocaleString()}`
  }

  /**
   * Get resource type display name
   */
  static getResourceTypeDisplayName(resourceType: string): string {
    const displayNames: Record<string, string> = {
      'content_generation': 'Content Generation',
      'api_calls': 'API Calls',
      'storage': 'Storage (MB)',
      'team_members': 'Team Members',
      'brands': 'Brands',
      'templates': 'Templates',
      'exports': 'Exports',
      'integrations': 'Integrations'
    }

    return displayNames[resourceType] || resourceType
  }

  /**
   * Get all usage limits for a user
   */
  static async getAllUsageLimits(userId: string): Promise<Record<string, UsageLimit>> {
    const resourceTypes = [
      'content_generation',
      'api_calls',
      'storage',
      'team_members',
      'brands',
      'templates',
      'exports'
    ]

    const limits: Record<string, UsageLimit> = {}

    await Promise.all(
      resourceTypes.map(async (resourceType) => {
        const limit = await this.checkUsageLimit(userId, resourceType)
        if (limit) {
          limits[resourceType] = limit
        }
      })
    )

    return limits
  }

  /**
   * Reset usage for testing (admin only)
   */
  static async resetUsage(
    userId: string,
    resourceType?: string
  ): Promise<boolean> {
    try {
      let query = this.supabase
        .from('usage_tracking')
        .delete()
        .eq('user_id', userId)

      if (resourceType) {
        query = query.eq('resource_type', resourceType)
      }

      const { error } = await query

      if (error) throw error
      return true
    } catch (error) {
      console.error('Error resetting usage:', error)
      return false
    }
  }
}
