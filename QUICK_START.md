# ViralPath.ai Quick Start Guide

## 🚨 Resolving "No brand data found" Error

If you're seeing the message "No brand data found - Please complete the onboarding process first," follow these steps:

### Step 1: Complete Supabase Setup

1. **Check Environment Variables**
   - Ensure you have a `.env.local` file in your project root
   - Verify it contains your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
   ```

2. **Set Up Database Tables**
   - Go to your Supabase dashboard → SQL Editor
   - Copy and run the SQL from `supabase/migrations/001_create_brands_and_calendars.sql`
   - This creates the necessary tables for brand management

### Step 2: Create Your First Brand

1. **Access Brand Manager**
   - Log into your ViralPath.ai dashboard
   - Click the "Select Brand" button in the top navigation
   - This opens the Brand Manager modal

2. **Create New Brand**
   - Click "New Brand" or "Create Your First Brand"
   - Fill in the required information:
     - **Brand Name**: Your company/brand name
     - **Industry**: e.g., Technology, Fashion, Food
     - **Tone**: Select from Professional, Casual, Friendly, etc.
     - **Target Audience**: Describe your ideal customers
     - **Platforms**: Select your social media platforms
     - **Description**: Brief brand description (optional)
     - **Website**: Your website URL (optional)

3. **Save and Activate**
   - Click "Save Brand"
   - Your new brand will automatically become active
   - You'll see it selected in the brand dropdown

### Step 3: Generate Your First Content Calendar

1. **Verify Brand Selection**
   - Ensure your brand name appears in the dropdown (not "Select Brand")
   - If not, click the dropdown and select your brand

2. **Generate Content**
   - Click "Generate Calendar" in the dashboard
   - Wait for AI to create your personalized content
   - Review and edit the generated posts as needed

## 🔧 Troubleshooting Common Issues

### "Invalid API key" Error
- Double-check your Supabase URL and anon key in `.env.local`
- Restart your development server: `npm run dev`
- Ensure you're using the **anon/public** key, not the service role key

### "Authentication Error"
- Verify email authentication is enabled in Supabase
- Check if you need to confirm your email address
- Try signing out and signing in again

### "Database Error"
- Ensure the migration SQL was executed successfully
- Check Supabase logs for specific error messages
- Verify Row Level Security policies are active

### Brand Manager Not Loading
- Check browser console for JavaScript errors
- Ensure all dependencies are installed: `npm install`
- Verify the BrandManager component is properly imported

## 📋 Quick Checklist

- [ ] Supabase project created
- [ ] Database migration executed
- [ ] Environment variables configured
- [ ] Development server running (`npm run dev`)
- [ ] User account created and verified
- [ ] First brand created and active
- [ ] Content calendar generated successfully

## 🆘 Still Need Help?

1. **Check the Full Setup Guide**: See `SUPABASE_SETUP.md` for detailed instructions
2. **Review Error Messages**: Check browser console and Supabase logs
3. **Verify Dependencies**: Run `npm install` to ensure all packages are installed
4. **Database Status**: Confirm tables exist in your Supabase dashboard

## 🎯 Next Steps After Setup

1. **Explore Brand Management**
   - Create multiple brands for different projects
   - Experiment with different tones and platforms
   - Use the duplicate feature for similar brands

2. **Customize Content**
   - Edit generated posts to match your voice
   - Add your own hashtags and mentions
   - Schedule posts using the calendar interface

3. **Optimize Your Strategy**
   - Review which content types work best
   - Adjust your brand settings based on performance
   - Generate content for different months and campaigns

---

**Remember**: The brand management system requires a properly configured Supabase database. If you're still experiencing issues, ensure you've completed the database setup in `SUPABASE_SETUP.md` first.