-- Fix RLS policy conflicts for content_calendars table
-- This migration resolves conflicts between different policy names

-- Drop all existing policies on content_calendars table
DROP POLICY IF EXISTS "Users can view own content calendars" ON public.content_calendars;
DROP POLICY IF EXISTS "Users can view their own content calendars" ON public.content_calendars;
DROP POLICY IF EXISTS "Users can insert own content calendars" ON public.content_calendars;
DROP POLICY IF EXISTS "Users can insert their own content calendars" ON public.content_calendars;
DROP POLICY IF EXISTS "Users can update own content calendars" ON public.content_calendars;
DROP POLICY IF EXISTS "Users can update their own content calendars" ON public.content_calendars;
DROP POLICY IF EXISTS "Users can delete own content calendars" ON public.content_calendars;
DROP POLICY IF EXISTS "Users can delete their own content calendars" ON public.content_calendars;

-- Create clean, consistent policies for content_calendars
CREATE POLICY "content_calendars_select_policy" ON public.content_calendars
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "content_calendars_insert_policy" ON public.content_calendars
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "content_calendars_update_policy" ON public.content_calendars
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "content_calendars_delete_policy" ON public.content_calendars
    FOR DELETE USING (auth.uid() = user_id);

-- Ensure RLS is enabled
ALTER TABLE public.content_calendars ENABLE ROW LEVEL SECURITY;

-- Add comment for documentation
COMMENT ON TABLE public.content_calendars IS 'Content calendars with RLS policies - users can only access their own data';
